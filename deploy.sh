#!/bin/bash

# WonderMom Fest Deployment Script
# Usage: ./deploy.sh

set -e

echo "🚀 Starting WonderMom Fest deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root or with sudo
if [[ $EUID -eq 0 ]]; then
    print_warning "Running as root. Make sure file permissions are correct."
fi

# Pull latest changes
print_status "Pulling latest changes from repository..."
git pull origin main

# Activate virtual environment
print_status "Activating virtual environment..."
source venv/bin/activate

# Install/update dependencies
print_status "Installing Python dependencies..."
pip install -r requirements.txt

# Run database migrations
print_status "Running database migrations..."
python manage.py migrate

# Collect static files
print_status "Collecting static files..."
python manage.py collectstatic --noinput

# Set proper permissions
print_status "Setting proper file permissions..."
sudo chown -R www-data:www-data /var/www/fest.thewondermom.club

# Restart services
print_status "Restarting Gunicorn service..."
sudo systemctl restart fest-gunicorn.service

# Check service status
if sudo systemctl is-active --quiet fest-gunicorn.service; then
    print_status "✅ Gunicorn service is running"
else
    print_error "❌ Gunicorn service failed to start"
    sudo systemctl status fest-gunicorn.service
    exit 1
fi

# Reload Nginx
print_status "Reloading Nginx..."
sudo nginx -t && sudo systemctl reload nginx

# Final status check
print_status "Checking application status..."
if curl -f -s http://localhost:8001 > /dev/null; then
    print_status "✅ Application is responding correctly"
else
    print_error "❌ Application is not responding"
    exit 1
fi

print_status "🎉 Deployment completed successfully!"
print_status "Website is available at: http://fest.thewondermom.club"

# Show service status
echo ""
echo "Service Status:"
sudo systemctl status fest-gunicorn.service --no-pager -l
