#!/bin/bash

# WonderMom Fest Backup Script
# Usage: ./backup.sh

set -e

# Configuration
BACKUP_DIR="/var/backups/fest.thewondermom.club"
DATE=$(date +%Y%m%d_%H%M%S)
PROJECT_DIR="/var/www/fest.thewondermom.club"

# Database credentials from .env
DB_NAME="wmfest"
DB_USER="wmfest"
DB_PASSWORD="wmfestp@665@htSh"

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Create backup directory
sudo mkdir -p $BACKUP_DIR

print_status "🔄 Starting backup process..."

# Backup database
print_status "Backing up MySQL database..."
sudo mysqldump -u $DB_USER -p$DB_PASSWORD $DB_NAME > $BACKUP_DIR/database_$DATE.sql

# Backup project files (excluding venv and cache)
print_status "Backing up project files..."
sudo tar -czf $BACKUP_DIR/project_$DATE.tar.gz \
    --exclude='venv' \
    --exclude='__pycache__' \
    --exclude='*.pyc' \
    --exclude='.git' \
    --exclude='static' \
    --exclude='media' \
    -C $(dirname $PROJECT_DIR) $(basename $PROJECT_DIR)

# Backup configuration files
print_status "Backing up configuration files..."
sudo cp /etc/nginx/sites-available/fest.thewondermom.club* $BACKUP_DIR/ 2>/dev/null || true
sudo cp /etc/systemd/system/fest-gunicorn.service $BACKUP_DIR/ 2>/dev/null || true

# Set permissions
sudo chown -R www-data:www-data $BACKUP_DIR

# Clean old backups (keep last 7 days)
print_status "Cleaning old backups..."
sudo find $BACKUP_DIR -name "database_*.sql" -mtime +7 -delete 2>/dev/null || true
sudo find $BACKUP_DIR -name "project_*.tar.gz" -mtime +7 -delete 2>/dev/null || true

print_status "✅ Backup completed successfully!"
print_status "Backup location: $BACKUP_DIR"
print_status "Files created:"
ls -la $BACKUP_DIR/*$DATE*
