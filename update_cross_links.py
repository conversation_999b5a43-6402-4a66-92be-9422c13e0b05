#!/usr/bin/env python3

import os
import re

# All competition directories
competitions = [
    'baby-photography', 'canvas-of-dream', 'card-making', 'clay-creations', 
    'colors-in-motion', 'eco-warrior', 'fancy-dress', 'twinning-with-mom', 'waste-to-wow'
]

# Cross-links HTML
cross_links = '''<b>Other Competition Registrations</b><br>
Baby Photography: <a href="https://fest.thewondermom.club/baby-photography-register/" target="_blank">Register Here</a><br>
Canvas of Dream: <a href="https://fest.thewondermom.club/canvas-of-dream-register/" target="_blank">Register Here</a><br>
Character Carnival (Fancy Dress): <a href="https://fest.thewondermom.club/fancy-dress-register/" target="_blank">Register Here</a><br>
Card Making (MomCard Magic): <a href="https://fest.thewondermom.club/card-making-register/" target="_blank">Register Here</a><br>
Clay Creations: <a href="https://fest.thewondermom.club/clay-creations-register/" target="_blank">Register Here</a><br>
Colors in Motion: <a href="https://fest.thewondermom.club/colors-in-motion-register/" target="_blank">Register Here</a><br>
Eco Warrior: <a href="https://fest.thewondermom.club/eco-warrior-register/" target="_blank">Register Here</a><br>
Twinning with Mom: <a href="https://fest.thewondermom.club/twinning-with-mom-register/" target="_blank">Register Here</a><br>
Waste to Wow: <a href="https://fest.thewondermom.club/waste-to-wow-register/" target="_blank">Register Here</a><br><br>'''

def update_cross_links(comp_key):
    template_path = f'core/templates/registrations/{comp_key}/{comp_key.replace("-", "_")}_register.html'
    
    if not os.path.exists(template_path):
        print(f"Template not found: {template_path}")
        return
    
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Update cross-links section
        # Look for the pattern starting with <b>Our Top Competitions or <b>Other Competition
        pattern = r'<b>(?:Our Top Competitions & Registrations|Other Competition Registrations)</b><br>.*?<br><br>'
        
        if re.search(pattern, content, re.DOTALL):
            content = re.sub(pattern, cross_links, content, flags=re.DOTALL)
            
            with open(template_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ Updated cross-links: {comp_key}")
        else:
            print(f"⚠️  Cross-links pattern not found in: {comp_key}")
        
    except Exception as e:
        print(f"❌ Error updating {comp_key}: {e}")

# Update all templates
for comp_key in competitions:
    update_cross_links(comp_key)

print("\n🎉 Cross-links update completed!")
