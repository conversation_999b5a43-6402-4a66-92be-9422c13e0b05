#!/usr/bin/env python3

# Script to generate the remaining view functions
# Run this to generate the view code and then copy it to views.py

registrations = [
    ('canvas_of_dream', 'CanvasOfDreamRegisterForm', 'canvas-of-dream'),
    ('card_making', 'CardMakingRegisterForm', 'card-making'),
    ('clay_creations', 'ClayCreationsRegisterForm', 'clay-creations'),
    ('colors_in_motion', 'ColorsInMotionRegisterForm', 'colors-in-motion'),
    ('eco_warrior', 'EcoWarriorRegisterForm', 'eco-warrior'),
    ('fancy_dress', 'FancyDressRegisterForm', 'fancy-dress'),
    ('twinning_with_mom', 'TwinningWithMomRegisterForm', 'twinning-with-mom'),
    ('waste_to_wow', 'WasteToWowRegisterForm', 'waste-to-wow'),
]

for func_name, form_class, template_dir in registrations:
    print(f"""
def {func_name}_register(request):
    if request.method == 'POST':
        {func_name}_form = {form_class}(request.POST, request.FILES)
        if {func_name}_form.is_valid():
            {func_name}_form.save()
            
            # Send email
            subject = "Thank you for registering! Your tickets for you & your family have been confirmed!"
            from_email = settings.EMAIL_HOST_USER
            to_email = [{func_name}_form.cleaned_data['email']]
            
            html_template = get_template('tickets-email/mothers.html')
            html_content = html_template.render({{'name': {func_name}_form.cleaned_data['parents_name']}})
            
            email = EmailMultiAlternatives(subject, '', from_email, to_email)
            email.attach_alternative(html_content, "text/html")
            
            EmailThread(email).start()
            
            return JsonResponse({{'success': True}})
        else:
            return JsonResponse({{'success': False, 'errors': {func_name}_form.errors}})
    else:
        {func_name}_form = {form_class}()
    
    context = {{
        '{func_name}_form': {func_name}_form,
    }}
    return render(request, 'registrations/{template_dir}/{func_name}_register.html', context)


def referral_{func_name}_register(request, slug):
    referrals = Referral.objects.get(slug=slug)
    if request.method == 'POST':
        {func_name}_form = {form_class}(request.POST, request.FILES)
        if {func_name}_form.is_valid():
            {func_name}_register_instance = {func_name}_form.save(commit=False)
            {func_name}_register_instance.referrals = referrals
            {func_name}_register_instance.save()
            
            # Update referral count
            referrals.ref_count += 1
            referrals.save()
            
            # Send email
            subject = "Thank you for registering! Your tickets for you & your family have been confirmed!"
            from_email = settings.EMAIL_HOST_USER
            to_email = [{func_name}_form.cleaned_data['email']]
            
            html_template = get_template('tickets-email/mothers.html')
            html_content = html_template.render({{'name': {func_name}_form.cleaned_data['parents_name']}})
            
            email = EmailMultiAlternatives(subject, '', from_email, to_email)
            email.attach_alternative(html_content, "text/html")
            
            EmailThread(email).start()
            
            return JsonResponse({{'success': True}})
        else:
            return JsonResponse({{'success': False, 'errors': {func_name}_form.errors}})
    else:
        {func_name}_form = {form_class}()

    context = {{
        '{func_name}_form': {func_name}_form,
        'referrals': referrals,
    }}
    return render(request, 'registrations/{template_dir}/referral_{func_name}_register.html', context)
""")
