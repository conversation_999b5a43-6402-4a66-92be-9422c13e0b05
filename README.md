# WonderMom Fest Website

A Django-based website for the WonderMom Fest event, featuring registration forms, competitions, and event information.

## Features

- Event registration system
- Multiple competition categories (Dance, Singing, Drawing, Baby Photo, <PERSON>cy Dress)
- Responsive design with mobile optimization
- HTML minification and compression
- AWS S3 integration for media storage
- MySQL database backend
- Admin panel for content management

##Technology Stack

- **Backend**: Django 5.0.7
- **Database**: MySQL
- **Web Server**: Nginx + Gunicorn
- **Storage**: AWS S3 (DigitalOcean Spaces)
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap
- **Deployment**: Ubuntu Server with systemd

##  Prerequisites

- Ubuntu Server (20.04 LTS or later)
- Python 3.12+
- MySQL Server
- Nginx
- Domain name configured with DNS

##  Setup on New Server

### 1. System Dependencies

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y python3 python3-pip python3-venv python3-dev
sudo apt install -y mysql-server mysql-client libmysqlclient-dev
sudo apt install -y nginx certbot python3-certbot-nginx
sudo apt install -y git curl wget

# Install additional dependencies
sudo apt install -y build-essential libssl-dev libffi-dev
```

### 2. MySQL Database Setup

```bash
# Secure MySQL installation
sudo mysql_secure_installation

# Create database and user
sudo mysql -u root -p
```

```sql
CREATE DATABASE wmfest CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'wmfest'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON wmfest.* TO 'wmfest'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 3. Project Setup

```bash
# Create project directory
sudo mkdir -p /var/www/fest.thewondermom.club
cd /var/www/fest.thewondermom.club

# Clone repository
sudo git clone <repository-url> .

# Create virtual environment
sudo python3 -m venv venv
sudo chown -R www-data:www-data /var/www/fest.thewondermom.club

# Activate virtual environment and install dependencies
source venv/bin/activate
pip install -r requirements.txt
```

### 4. Environment Configuration

```bash
# Create environment file
cp .env.example .env
nano .env
```

Update `.env` with your configuration:
```env
SECRET_KEY=your_secret_key_here
DEBUG=False
ALLOWED_HOSTS=fest.thewondermom.club,www.fest.thewondermom.club

DB_NAME=wmfest
DB_USER=wmfest
DB_PASSWORD=your_secure_password
DB_HOST=localhost
DB_PORT=3306

AWS_S3_ACCESS_KEY_ID=your_access_key
AWS_S3_SECRET_ACCESS_KEY=your_secret_key
AWS_S3_ENDPOINT_URL=your_endpoint_url
AWS_S3_CUSTOM_DOMAIN=your_custom_domain
AWS_STORAGE_BUCKET_NAME=your_bucket_name

EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_email_password
```

### 5. Django Setup

```bash
# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Collect static files
python manage.py collectstatic --noinput

# Set proper permissions
sudo chown -R www-data:www-data /var/www/fest.thewondermom.club
```

### 6. Gunicorn Configuration

```bash
# Copy systemd service file
sudo cp fest-gunicorn.service /etc/systemd/system/

# Reload systemd and enable service
sudo systemctl daemon-reload
sudo systemctl enable fest-gunicorn.service
sudo systemctl start fest-gunicorn.service

# Check service status
sudo systemctl status fest-gunicorn.service
```

### 7. Nginx Configuration

```bash
# Copy nginx configuration
sudo cp fest.thewondermom.club.temp /etc/nginx/sites-available/fest.thewondermom.club.temp

# Enable site (temporary HTTP configuration)
sudo ln -s /etc/nginx/sites-available/fest.thewondermom.club.temp /etc/nginx/sites-enabled/

# Test nginx configuration
sudo nginx -t

# Reload nginx
sudo systemctl reload nginx
```

### 8. SSL Certificate Setup

```bash
# Obtain SSL certificate (ensure DNS is configured first)
sudo certbot --nginx -d fest.thewondermom.club -d www.fest.thewondermom.club

# Switch to SSL configuration
sudo rm /etc/nginx/sites-enabled/fest.thewondermom.club.temp
sudo cp fest.thewondermom.club /etc/nginx/sites-available/
sudo ln -s /etc/nginx/sites-available/fest.thewondermom.club /etc/nginx/sites-enabled/

# Test and reload nginx
sudo nginx -t && sudo systemctl reload nginx
```

### 9. DNS Configuration (GoDaddy)

1. Login to GoDaddy DNS Management
2. Add A Record:
   - **Type**: A
   - **Name**: fest
   - **Value**: [Your Server IP]
   - **TTL**: 600

3. Add CNAME Record:
   - **Type**: CNAME
   - **Name**: www.fest
   - **Value**: fest.thewondermom.club
   - **TTL**: 600

## 🔧 Management Commands

```bash
# Check application status
sudo systemctl status fest-gunicorn.service
curl -I http://localhost:8001

# View logs
sudo journalctl -u fest-gunicorn.service -f
sudo tail -f /var/log/nginx/fest.thewondermom.club.error.log

# Restart services
sudo systemctl restart fest-gunicorn.service
sudo systemctl reload nginx

# Django management
cd /var/www/fest.thewondermom.club
source venv/bin/activate
python manage.py migrate
python manage.py collectstatic --noinput
```

## 📁 Project Structure

```
fest.thewondermom.club/
├── core/                   # Main Django app
├── fest/                   # Django project settings
├── static/                 # Static files (collected)
├── media/                  # Media files (uploads)
├── venv/                   # Virtual environment
├── requirements.txt        # Python dependencies
├── .env                    # Environment variables
├── fest-gunicorn.service   # Systemd service file
├── fest.thewondermom.club  # Nginx SSL configuration
├── fest.thewondermom.club.temp # Nginx HTTP configuration
└── manage.py              # Django management script
```

## 🔒 Security Features

- HTTPS enforcement with SSL certificates
- Security headers (HSTS, XSS Protection, etc.)
- CSRF protection
- SQL injection protection via Django ORM
- File upload restrictions
- Environment-based configuration

## 🚨 Troubleshooting

### Common Issues

1. **500 Internal Server Error**
   ```bash
   sudo journalctl -u fest-gunicorn.service -n 20
   ```

2. **Static files not loading**
   ```bash
   python manage.py collectstatic --noinput
   sudo systemctl restart fest-gunicorn.service
   ```

3. **Database connection issues**
   - Check MySQL service: `sudo systemctl status mysql`
   - Verify credentials in `.env` file
   - Test connection: `python manage.py dbshell`

4. **SSL certificate issues**
   ```bash
   sudo certbot certificates
   sudo certbot renew --dry-run
   ```

## 📞 Support

For technical support or deployment assistance, contact the development team.

