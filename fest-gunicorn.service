[Unit]
Description=Gunicorn instance to serve fest.thewondermom.club
After=network.target

[Service]
Type=exec
# the specific user that our service will run as
User=www-data
Group=www-data
# another option for an even more restricted service is
# DynamicUser=yes
# see http://0pointer.net/blog/dynamic-users-with-systemd.html
RuntimeDirectory=gunicorn
WorkingDirectory=/var/www/fest.thewondermom.club
ExecStart=/var/www/fest.thewondermom.club/venv/bin/gunicorn \
          --access-logfile - \
          --error-logfile - \
          --workers 3 \
          --worker-class sync \
          --timeout 120 \
          --max-requests 1000 \
          --max-requests-jitter 100 \
          --preload \
          --bind 127.0.0.1:8001 \
          fest.wsgi:application
ExecReload=/bin/kill -s HUP $MAINPID
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true

[Install]
WantedBy=multi-user.target
