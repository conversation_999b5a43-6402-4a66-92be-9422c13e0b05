from django import forms
from core.models import (<PERSON><PERSON><PERSON><PERSON><PERSON>, DrawingRegister, <PERSON>Reg<PERSON>, <PERSON>Reg<PERSON>, MotherRegister, <PERSON>ick<PERSON>, BabyPhotoRegister,
                        BabyPhotographyRegister, CanvasOfDreamRegister, CardMakingRegister, ClayCreationsRegister,
                        ColorsInMotionRegister, EcoWarriorRegister, FancyDressRegister, TwinningWithMomRegister, WasteToWowRegister)

class MotherRegisterForm(forms.ModelForm):
    class Meta:
        model = MotherRegister
        fields = '__all__'


class TicketsForm(forms.ModelForm):
    class Meta:
        model = Tickets
        fields = '__all__'

class FancyRegisterForm(forms.ModelForm):
    class Meta:
        model = FancyRegister
        fields = '__all__'


class DrawingRegisterForm(forms.ModelForm):
    class Meta:
        model = DrawingRegister
        fields = '__all__'

class SingingRegisterForm(forms.ModelForm):
    class Meta:
        model = SingingRegister
        fields = '__all__'


class DanceRegisterForm(forms.ModelForm):
    class Meta:
        model = DanceRegister
        fields = '__all__'



class BabyPhotoRegisterForm(forms.ModelForm):
    class Meta:
        model = BabyPhotoRegister
        fields = '__all__'


# New Registration Forms
class BabyPhotographyRegisterForm(forms.ModelForm):
    class Meta:
        model = BabyPhotographyRegister
        fields = '__all__'


class CanvasOfDreamRegisterForm(forms.ModelForm):
    class Meta:
        model = CanvasOfDreamRegister
        fields = '__all__'


class CardMakingRegisterForm(forms.ModelForm):
    class Meta:
        model = CardMakingRegister
        fields = '__all__'


class ClayCreationsRegisterForm(forms.ModelForm):
    class Meta:
        model = ClayCreationsRegister
        fields = '__all__'


class ColorsInMotionRegisterForm(forms.ModelForm):
    class Meta:
        model = ColorsInMotionRegister
        fields = '__all__'


class EcoWarriorRegisterForm(forms.ModelForm):
    class Meta:
        model = EcoWarriorRegister
        fields = '__all__'


class FancyDressRegisterForm(forms.ModelForm):
    class Meta:
        model = FancyDressRegister
        fields = '__all__'


class TwinningWithMomRegisterForm(forms.ModelForm):
    class Meta:
        model = TwinningWithMomRegister
        fields = '__all__'


class WasteToWowRegisterForm(forms.ModelForm):
    class Meta:
        model = WasteToWowRegister
        fields = '__all__'

