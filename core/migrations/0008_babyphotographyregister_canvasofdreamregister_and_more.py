# Generated by Django 5.0.7 on 2025-08-22 22:31

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0007_alter_banner_image_alter_banner_mobile_image'),
    ]

    operations = [
        migrations.CreateModel(
            name='BabyPhotographyRegister',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('parents_name', models.CharField(blank=True, max_length=1025, null=True)),
                ('phone', models.CharField(blank=True, max_length=1025, null=True)),
                ('email', models.CharField(blank=True, max_length=1025, null=True)),
                ('child_name', models.CharField(blank=True, max_length=1025, null=True)),
                ('child_age', models.CharField(blank=True, max_length=1025, null=True)),
                ('nationality', models.Char<PERSON><PERSON>(blank=True, max_length=1025, null=True)),
                ('about', models.TextField(blank=True, null=True)),
                ('photo', models.FileField(blank=True, null=True, upload_to='Baby Photography Register')),
                ('insta_link', models.CharField(blank=True, max_length=255, null=True)),
                ('created_date', models.DateField(auto_now=True, null=True)),
                ('updated_date', models.DateField(auto_now_add=True, null=True)),
                ('status', models.CharField(blank=True, choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('Rejected', 'Rejected')], max_length=1025, null=True)),
                ('referrals', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.referral')),
            ],
        ),
        migrations.CreateModel(
            name='CanvasOfDreamRegister',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('parents_name', models.CharField(blank=True, max_length=1025, null=True)),
                ('phone', models.CharField(blank=True, max_length=1025, null=True)),
                ('email', models.CharField(blank=True, max_length=1025, null=True)),
                ('child_name', models.CharField(blank=True, max_length=1025, null=True)),
                ('child_age', models.CharField(blank=True, max_length=1025, null=True)),
                ('nationality', models.CharField(blank=True, max_length=1025, null=True)),
                ('about', models.TextField(blank=True, null=True)),
                ('photo', models.FileField(blank=True, null=True, upload_to='Canvas Of Dream Register')),
                ('insta_link', models.CharField(blank=True, max_length=255, null=True)),
                ('created_date', models.DateField(auto_now=True, null=True)),
                ('updated_date', models.DateField(auto_now_add=True, null=True)),
                ('status', models.CharField(blank=True, choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('Rejected', 'Rejected')], max_length=1025, null=True)),
                ('referrals', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.referral')),
            ],
        ),
        migrations.CreateModel(
            name='CardMakingRegister',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('parents_name', models.CharField(blank=True, max_length=1025, null=True)),
                ('phone', models.CharField(blank=True, max_length=1025, null=True)),
                ('email', models.CharField(blank=True, max_length=1025, null=True)),
                ('child_name', models.CharField(blank=True, max_length=1025, null=True)),
                ('child_age', models.CharField(blank=True, max_length=1025, null=True)),
                ('nationality', models.CharField(blank=True, max_length=1025, null=True)),
                ('about', models.TextField(blank=True, null=True)),
                ('photo', models.FileField(blank=True, null=True, upload_to='Card Making Register')),
                ('insta_link', models.CharField(blank=True, max_length=255, null=True)),
                ('created_date', models.DateField(auto_now=True, null=True)),
                ('updated_date', models.DateField(auto_now_add=True, null=True)),
                ('status', models.CharField(blank=True, choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('Rejected', 'Rejected')], max_length=1025, null=True)),
                ('referrals', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.referral')),
            ],
        ),
        migrations.CreateModel(
            name='ClayCreationsRegister',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('parents_name', models.CharField(blank=True, max_length=1025, null=True)),
                ('phone', models.CharField(blank=True, max_length=1025, null=True)),
                ('email', models.CharField(blank=True, max_length=1025, null=True)),
                ('child_name', models.CharField(blank=True, max_length=1025, null=True)),
                ('child_age', models.CharField(blank=True, max_length=1025, null=True)),
                ('nationality', models.CharField(blank=True, max_length=1025, null=True)),
                ('about', models.TextField(blank=True, null=True)),
                ('photo', models.FileField(blank=True, null=True, upload_to='Clay Creations Register')),
                ('insta_link', models.CharField(blank=True, max_length=255, null=True)),
                ('created_date', models.DateField(auto_now=True, null=True)),
                ('updated_date', models.DateField(auto_now_add=True, null=True)),
                ('status', models.CharField(blank=True, choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('Rejected', 'Rejected')], max_length=1025, null=True)),
                ('referrals', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.referral')),
            ],
        ),
        migrations.CreateModel(
            name='ColorsInMotionRegister',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('parents_name', models.CharField(blank=True, max_length=1025, null=True)),
                ('phone', models.CharField(blank=True, max_length=1025, null=True)),
                ('email', models.CharField(blank=True, max_length=1025, null=True)),
                ('child_name', models.CharField(blank=True, max_length=1025, null=True)),
                ('child_age', models.CharField(blank=True, max_length=1025, null=True)),
                ('nationality', models.CharField(blank=True, max_length=1025, null=True)),
                ('about', models.TextField(blank=True, null=True)),
                ('photo', models.FileField(blank=True, null=True, upload_to='Colors In Motion Register')),
                ('insta_link', models.CharField(blank=True, max_length=255, null=True)),
                ('created_date', models.DateField(auto_now=True, null=True)),
                ('updated_date', models.DateField(auto_now_add=True, null=True)),
                ('status', models.CharField(blank=True, choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('Rejected', 'Rejected')], max_length=1025, null=True)),
                ('referrals', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.referral')),
            ],
        ),
        migrations.CreateModel(
            name='EcoWarriorRegister',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('parents_name', models.CharField(blank=True, max_length=1025, null=True)),
                ('phone', models.CharField(blank=True, max_length=1025, null=True)),
                ('email', models.CharField(blank=True, max_length=1025, null=True)),
                ('child_name', models.CharField(blank=True, max_length=1025, null=True)),
                ('child_age', models.CharField(blank=True, max_length=1025, null=True)),
                ('nationality', models.CharField(blank=True, max_length=1025, null=True)),
                ('about', models.TextField(blank=True, null=True)),
                ('photo', models.FileField(blank=True, null=True, upload_to='Eco Warrior Register')),
                ('insta_link', models.CharField(blank=True, max_length=255, null=True)),
                ('created_date', models.DateField(auto_now=True, null=True)),
                ('updated_date', models.DateField(auto_now_add=True, null=True)),
                ('status', models.CharField(blank=True, choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('Rejected', 'Rejected')], max_length=1025, null=True)),
                ('referrals', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.referral')),
            ],
        ),
        migrations.CreateModel(
            name='FancyDressRegister',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('parents_name', models.CharField(blank=True, max_length=1025, null=True)),
                ('phone', models.CharField(blank=True, max_length=1025, null=True)),
                ('email', models.CharField(blank=True, max_length=1025, null=True)),
                ('child_name', models.CharField(blank=True, max_length=1025, null=True)),
                ('child_age', models.CharField(blank=True, max_length=1025, null=True)),
                ('nationality', models.CharField(blank=True, max_length=1025, null=True)),
                ('about', models.TextField(blank=True, null=True)),
                ('photo', models.FileField(blank=True, null=True, upload_to='Fancy Dress Register')),
                ('insta_link', models.CharField(blank=True, max_length=255, null=True)),
                ('created_date', models.DateField(auto_now=True, null=True)),
                ('updated_date', models.DateField(auto_now_add=True, null=True)),
                ('status', models.CharField(blank=True, choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('Rejected', 'Rejected')], max_length=1025, null=True)),
                ('referrals', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.referral')),
            ],
        ),
        migrations.CreateModel(
            name='TwinningWithMomRegister',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('parents_name', models.CharField(blank=True, max_length=1025, null=True)),
                ('phone', models.CharField(blank=True, max_length=1025, null=True)),
                ('email', models.CharField(blank=True, max_length=1025, null=True)),
                ('child_name', models.CharField(blank=True, max_length=1025, null=True)),
                ('child_age', models.CharField(blank=True, max_length=1025, null=True)),
                ('nationality', models.CharField(blank=True, max_length=1025, null=True)),
                ('about', models.TextField(blank=True, null=True)),
                ('photo', models.FileField(blank=True, null=True, upload_to='Twinning With Mom Register')),
                ('insta_link', models.CharField(blank=True, max_length=255, null=True)),
                ('created_date', models.DateField(auto_now=True, null=True)),
                ('updated_date', models.DateField(auto_now_add=True, null=True)),
                ('status', models.CharField(blank=True, choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('Rejected', 'Rejected')], max_length=1025, null=True)),
                ('referrals', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.referral')),
            ],
        ),
        migrations.CreateModel(
            name='WasteToWowRegister',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('parents_name', models.CharField(blank=True, max_length=1025, null=True)),
                ('phone', models.CharField(blank=True, max_length=1025, null=True)),
                ('email', models.CharField(blank=True, max_length=1025, null=True)),
                ('child_name', models.CharField(blank=True, max_length=1025, null=True)),
                ('child_age', models.CharField(blank=True, max_length=1025, null=True)),
                ('nationality', models.CharField(blank=True, max_length=1025, null=True)),
                ('about', models.TextField(blank=True, null=True)),
                ('photo', models.FileField(blank=True, null=True, upload_to='Waste To Wow Register')),
                ('insta_link', models.CharField(blank=True, max_length=255, null=True)),
                ('created_date', models.DateField(auto_now=True, null=True)),
                ('updated_date', models.DateField(auto_now_add=True, null=True)),
                ('status', models.CharField(blank=True, choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('Rejected', 'Rejected')], max_length=1025, null=True)),
                ('referrals', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.referral')),
            ],
        ),
    ]
