# Generated by Django 5.0.7 on 2024-08-08 10:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='babyphotoregister',
            name='child_age',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='babyphotoregister',
            name='child_name',
            field=models.Char<PERSON>ield(blank=True, max_length=1025, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='babyphotoregister',
            name='email',
            field=models.Char<PERSON>ield(blank=True, max_length=1025, null=True),
        ),
        migrations.Alter<PERSON>ield(
            model_name='babyphotoregister',
            name='nationality',
            field=models.Char<PERSON><PERSON>(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='babyphotoregister',
            name='parents_name',
            field=models.Char<PERSON><PERSON>(blank=True, max_length=1025, null=True),
        ),
        migrations.<PERSON><PERSON><PERSON><PERSON>(
            model_name='babyphotoregister',
            name='phone',
            field=models.<PERSON><PERSON><PERSON><PERSON>(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='babyphotoregister',
            name='status',
            field=models.CharField(blank=True, choices=[('Approved', 'Approved'), ('Pending', 'Pending')], max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='danceregister',
            name='child_age',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='danceregister',
            name='child_name',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='danceregister',
            name='email',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='danceregister',
            name='insta_link',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='danceregister',
            name='nationality',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='danceregister',
            name='parents_name',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='danceregister',
            name='phone',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='danceregister',
            name='status',
            field=models.CharField(blank=True, choices=[('Approved', 'Approved'), ('Pending', 'Pending')], max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='drawingregister',
            name='child_age',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='drawingregister',
            name='child_name',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='drawingregister',
            name='email',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='drawingregister',
            name='insta_link',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='drawingregister',
            name='nationality',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='drawingregister',
            name='parents_name',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='drawingregister',
            name='phone',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='drawingregister',
            name='status',
            field=models.CharField(blank=True, choices=[('Approved', 'Approved'), ('Pending', 'Pending')], max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='fancyregister',
            name='child_age',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='fancyregister',
            name='child_name',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='fancyregister',
            name='email',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='fancyregister',
            name='insta_link',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='fancyregister',
            name='nationality',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='fancyregister',
            name='parents_name',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='fancyregister',
            name='phone',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='fancyregister',
            name='status',
            field=models.CharField(blank=True, choices=[('Approved', 'Approved'), ('Pending', 'Pending')], max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='motherregister',
            name='age_group',
            field=models.CharField(blank=True, choices=[('Expecting Mom', 'Expecting Mom'), ('0-3', '0-3'), ('3-12', '3-12'), ('Others', 'Others')], max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='motherregister',
            name='email',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='motherregister',
            name='emirates',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='motherregister',
            name='insta_link',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='motherregister',
            name='name',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='motherregister',
            name='nationality',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='motherregister',
            name='phone',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='motherregister',
            name='status',
            field=models.CharField(blank=True, choices=[('Approved', 'Approved'), ('Pending', 'Pending')], max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='singingregister',
            name='child_age',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='singingregister',
            name='child_name',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='singingregister',
            name='email',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='singingregister',
            name='insta_link',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='singingregister',
            name='nationality',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='singingregister',
            name='parents_name',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='singingregister',
            name='phone',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='singingregister',
            name='status',
            field=models.CharField(blank=True, choices=[('Approved', 'Approved'), ('Pending', 'Pending')], max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='tickets',
            name='email',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='tickets',
            name='emirates',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='tickets',
            name='members',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='tickets',
            name='name',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='tickets',
            name='nationality',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='tickets',
            name='phone',
            field=models.CharField(blank=True, max_length=1025, null=True),
        ),
        migrations.AlterField(
            model_name='tickets',
            name='status',
            field=models.CharField(blank=True, choices=[('Approved', 'Approved'), ('Pending', 'Pending')], max_length=1025, null=True),
        ),
    ]
