# Generated by Django 5.0.7 on 2024-08-05 13:30

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Banner',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_no', models.IntegerField()),
                ('name', models.CharField(max_length=255)),
                ('image', models.FileField(default='Banner', upload_to='')),
                ('mobile_image', models.FileField(default='Mobile Banner', upload_to='')),
                ('link', models.URLField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_no', models.IntegerField()),
                ('title', models.Char<PERSON>ield(max_length=255)),
                ('image', models.FileField(upload_to='Category')),
                ('link', models.URLField()),
                ('created_date', models.DateField(auto_now=True)),
                ('updated_date', models.DateField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='Partners',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_no', models.IntegerField()),
                ('name', models.CharField(max_length=255)),
                ('image', models.FileField(upload_to='Partners')),
                ('link', models.URLField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Referral',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=255, null=True)),
                ('phone', models.CharField(blank=True, max_length=255, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('nationality', models.CharField(blank=True, max_length=255, null=True)),
                ('ref_count', models.CharField(blank=True, max_length=255, null=True)),
                ('slug', models.SlugField(blank=True, max_length=200, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name='MotherRegister',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('photo', models.FileField(blank=True, null=True, upload_to='Mother Register')),
                ('name', models.CharField(blank=True, max_length=255, null=True)),
                ('phone', models.CharField(blank=True, max_length=255, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('nationality', models.CharField(blank=True, max_length=255, null=True)),
                ('age_group', models.CharField(blank=True, choices=[('Expecting Mom', 'Expecting Mom'), ('0-3', '0-3'), ('3-12', '3-12'), ('Others', 'Others')], max_length=255, null=True)),
                ('emirates', models.CharField(blank=True, max_length=255, null=True)),
                ('insta_link', models.CharField(blank=True, max_length=255, null=True)),
                ('created_date', models.DateField(auto_now=True, null=True)),
                ('updated_date', models.DateField(auto_now_add=True, null=True)),
                ('status', models.CharField(blank=True, choices=[('Approved', 'Approved'), ('Pending', 'Pending')], max_length=255, null=True)),
                ('referrals', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.referral')),
            ],
        ),
        migrations.CreateModel(
            name='FancyRegister',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('parents_name', models.CharField(blank=True, max_length=255, null=True)),
                ('phone', models.CharField(blank=True, max_length=255, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('child_name', models.CharField(blank=True, max_length=255, null=True)),
                ('child_age', models.CharField(blank=True, max_length=255, null=True)),
                ('nationality', models.CharField(blank=True, max_length=255, null=True)),
                ('about', models.TextField(blank=True, null=True)),
                ('photo', models.FileField(blank=True, null=True, upload_to='Fancy Register')),
                ('insta_link', models.CharField(blank=True, max_length=255, null=True)),
                ('created_date', models.DateField(auto_now=True, null=True)),
                ('updated_date', models.DateField(auto_now_add=True, null=True)),
                ('status', models.CharField(blank=True, choices=[('Approved', 'Approved'), ('Pending', 'Pending')], max_length=255, null=True)),
                ('referrals', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.referral')),
            ],
        ),
        migrations.CreateModel(
            name='DrawingRegister',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('parents_name', models.CharField(blank=True, max_length=255, null=True)),
                ('phone', models.CharField(blank=True, max_length=255, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('child_name', models.CharField(blank=True, max_length=255, null=True)),
                ('child_age', models.CharField(blank=True, max_length=255, null=True)),
                ('nationality', models.CharField(blank=True, max_length=255, null=True)),
                ('about', models.TextField(blank=True, null=True)),
                ('photo', models.FileField(blank=True, null=True, upload_to='Drawing Register')),
                ('insta_link', models.CharField(blank=True, max_length=255, null=True)),
                ('created_date', models.DateField(auto_now=True, null=True)),
                ('updated_date', models.DateField(auto_now_add=True, null=True)),
                ('status', models.CharField(blank=True, choices=[('Approved', 'Approved'), ('Pending', 'Pending')], max_length=255, null=True)),
                ('referrals', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.referral')),
            ],
        ),
        migrations.CreateModel(
            name='DanceRegister',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('parents_name', models.CharField(blank=True, max_length=255, null=True)),
                ('phone', models.CharField(blank=True, max_length=255, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('child_name', models.CharField(blank=True, max_length=255, null=True)),
                ('child_age', models.CharField(blank=True, max_length=255, null=True)),
                ('nationality', models.CharField(blank=True, max_length=255, null=True)),
                ('about', models.TextField(blank=True, null=True)),
                ('photo', models.FileField(blank=True, null=True, upload_to='Dance Register')),
                ('insta_link', models.CharField(blank=True, max_length=255, null=True)),
                ('created_date', models.DateField(auto_now=True, null=True)),
                ('updated_date', models.DateField(auto_now_add=True, null=True)),
                ('status', models.CharField(blank=True, choices=[('Approved', 'Approved'), ('Pending', 'Pending')], max_length=255, null=True)),
                ('referrals', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.referral')),
            ],
        ),
        migrations.CreateModel(
            name='BabyPhotoRegister',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('parents_name', models.CharField(blank=True, max_length=255, null=True)),
                ('phone', models.CharField(blank=True, max_length=255, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('child_name', models.CharField(blank=True, max_length=255, null=True)),
                ('child_age', models.CharField(blank=True, max_length=255, null=True)),
                ('nationality', models.CharField(blank=True, max_length=255, null=True)),
                ('about', models.TextField(blank=True, null=True)),
                ('photo', models.FileField(blank=True, null=True, upload_to='Baby Photo Register')),
                ('insta_link', models.CharField(blank=True, max_length=255, null=True)),
                ('created_date', models.DateField(auto_now=True, null=True)),
                ('updated_date', models.DateField(auto_now_add=True, null=True)),
                ('status', models.CharField(blank=True, choices=[('Approved', 'Approved'), ('Pending', 'Pending')], max_length=255, null=True)),
                ('referrals', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.referral')),
            ],
        ),
        migrations.CreateModel(
            name='SingingRegister',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('parents_name', models.CharField(blank=True, max_length=255, null=True)),
                ('phone', models.CharField(blank=True, max_length=255, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('child_name', models.CharField(blank=True, max_length=255, null=True)),
                ('child_age', models.CharField(blank=True, max_length=255, null=True)),
                ('nationality', models.CharField(blank=True, max_length=255, null=True)),
                ('about', models.TextField(blank=True, null=True)),
                ('photo', models.FileField(blank=True, null=True, upload_to='Singing Register')),
                ('insta_link', models.CharField(blank=True, max_length=255, null=True)),
                ('created_date', models.DateField(auto_now=True, null=True)),
                ('updated_date', models.DateField(auto_now_add=True, null=True)),
                ('status', models.CharField(blank=True, choices=[('Approved', 'Approved'), ('Pending', 'Pending')], max_length=255, null=True)),
                ('referrals', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.referral')),
            ],
        ),
        migrations.CreateModel(
            name='Tickets',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(blank=True, max_length=255, null=True)),
                ('phone', models.CharField(blank=True, max_length=255, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('nationality', models.CharField(blank=True, max_length=255, null=True)),
                ('emirates', models.CharField(blank=True, max_length=255, null=True)),
                ('members', models.CharField(blank=True, max_length=255, null=True)),
                ('created_date', models.DateField(auto_now=True, null=True)),
                ('updated_date', models.DateField(auto_now_add=True, null=True)),
                ('status', models.CharField(blank=True, choices=[('Approved', 'Approved'), ('Pending', 'Pending')], max_length=255, null=True)),
                ('referrals', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.referral')),
            ],
        ),
    ]
