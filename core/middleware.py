from django.utils.deprecation import MiddlewareMixin
from htmlmin.minify import html_minify
import gzip
from io import BytesIO

class HtmlMinifyMiddleware(MiddlewareMixin):
    def process_response(self, request, response):
        # Only process HTML content
        if 'text/html' in response['Content-Type']:
            # Check if the content is gzipped
            if response.has_header('Content-Encoding') and response['Content-Encoding'] == 'gzip':
                # Decompress the content
                buffer = BytesIO(response.content)
                with gzip.GzipFile(fileobj=buffer, mode='rb') as gz:
                    content = gz.read()
                # Minify the decompressed content
                minified_content = html_minify(content.decode('utf-8'), ignore_comments=True)
                # Re-compress the minified content
                buffer = BytesIO()
                with gzip.GzipFile(fileobj=buffer, mode='wb') as gz:
                    gz.write(minified_content.encode('utf-8'))
                response.content = buffer.getvalue()
                response['Content-Length'] = str(len(response.content))
            else:
                # Minify if not compressed
                minified_content = html_minify(response.content.decode('utf-8'), ignore_comments=True)
                response.content = minified_content.encode('utf-8')
                response['Content-Length'] = str(len(response.content))
        return response
