from django.urls import path
from core import views

urlpatterns = [
    path('', views.home, name='home'),
    path('category', views.category, name='category'),
    path('guidelines', views.guidelines, name='guidelines'),

    path('mother-register/', views.mother_register, name='mother_register'),
    path('mother-register/<slug:slug>', views.referral_mother_register, name='referral_mother_register'),

    path('fancy-register/', views.fancy_register, name='fancy_register'),
    path('fancy-register/<slug:slug>', views.referral_fancy_register, name='referral_fancy_register'),

    path('drawing-register/', views.drawing_register, name='drawing_register'),
    path('drawing-register/<slug:slug>', views.referral_drawing_register, name='referral_drawing_register'),

    path('singing-register/', views.singing_register, name='singing_register'),
    path('singing-register/<slug:slug>', views.referral_singing_register, name='referral_singing_register'),

    path('dance-register/', views.dance_register, name='dance_register'),
    path('dance-register/<slug:slug>', views.referral_dance_register, name='referral_dance_register'),

    path('referral/<slug:slug>', views.referral_category, name='referral_category'),

    path('tickets', views.tickets, name='tickets'),
    path('tickets/<slug:slug>', views.referral_tickets, name='referral_tickets'),

    path('baby-photo-register/', views.baby_photo_register, name='baby_photo_register'),
    path('baby-photo-register/<slug:slug>', views.referral_baby_photo_register, name='referral_baby_photo_register'),

    # New Registration URLs
    path('baby-photography-register/', views.baby_photography_register, name='baby_photography_register'),
    path('baby-photography-register/<slug:slug>', views.referral_baby_photography_register, name='referral_baby_photography_register'),

    path('canvas-of-dream-register/', views.canvas_of_dream_register, name='canvas_of_dream_register'),
    path('canvas-of-dream-register/<slug:slug>', views.referral_canvas_of_dream_register, name='referral_canvas_of_dream_register'),

    path('card-making-register/', views.card_making_register, name='card_making_register'),
    path('card-making-register/<slug:slug>', views.referral_card_making_register, name='referral_card_making_register'),

    path('clay-creations-register/', views.clay_creations_register, name='clay_creations_register'),
    path('clay-creations-register/<slug:slug>', views.referral_clay_creations_register, name='referral_clay_creations_register'),

    path('colors-in-motion-register/', views.colors_in_motion_register, name='colors_in_motion_register'),
    path('colors-in-motion-register/<slug:slug>', views.referral_colors_in_motion_register, name='referral_colors_in_motion_register'),

    path('eco-warrior-register/', views.eco_warrior_register, name='eco_warrior_register'),
    path('eco-warrior-register/<slug:slug>', views.referral_eco_warrior_register, name='referral_eco_warrior_register'),

    path('fancy-dress-register/', views.fancy_dress_register, name='fancy_dress_register'),
    path('fancy-dress-register/<slug:slug>', views.referral_fancy_dress_register, name='referral_fancy_dress_register'),

    path('twinning-with-mom-register/', views.twinning_with_mom_register, name='twinning_with_mom_register'),
    path('twinning-with-mom-register/<slug:slug>', views.referral_twinning_with_mom_register, name='referral_twinning_with_mom_register'),

    path('waste-to-wow-register/', views.waste_to_wow_register, name='waste_to_wow_register'),
    path('waste-to-wow-register/<slug:slug>', views.referral_waste_to_wow_register, name='referral_waste_to_wow_register'),

]