from django.http import JsonResponse
from django.shortcuts import render
from django.core.mail import EmailMessage, EmailMultiAlternatives
from django.template.loader import get_template
from django.conf import settings
import uuid
from core.forms import (Tick<PERSON>Form, BabyPhotoReg<PERSON>Form, MotherRegisterForm, FancyRegisterForm,
    DrawingRegisterForm, SingingRegisterForm, DanceRegisterForm, BabyPhotographyRegisterForm,
    CanvasOfDreamRegisterForm, CardMakingRegisterForm, ClayCreationsRegisterForm,
    ColorsInMotionRegisterForm, EcoWarriorRegisterForm, FancyDressRegisterForm,
    TwinningWithMomRegisterForm, WasteToWowRegisterForm)
from core.models import Partners, Category, Banner, Referral
import threading


class EmailThread(threading.Thread):
    def __init__(self, email_message):
        self.email_message = email_message
        threading.Thread.__init__(self)

    def run(self):
        self.email_message.send()

def home(request):
    # Simple test response to verify Django is working
    from django.http import HttpResponse
    return HttpResponse("""
    <html>
    <head><title>The Wonder Mom Club - Live!</title></head>
    <body>
        <h1>🎉 The Wonder Mom Club is LIVE! 🎉</h1>
        <p>Website is successfully running and operational.</p>
        <p>Django application is working properly.</p>
        <p>Database connectivity will be restored shortly.</p>
        <hr>
        <p><em>Temporary simple response for testing - Full website functionality coming soon!</em></p>
    </body>
    </html>
    """, content_type='text/html')

def guidelines(request):
    banner = Banner.objects.order_by('order_no')
    context = {
        'banner': banner,
    }
    return render(request, 'guidelines.html', context)

def category(request):
    banner = Banner.objects.order_by('order_no')
    category = Category.objects.order_by('order_no')
    context = {
        'category': category,
        'banner': banner,
    }
    return render(request, 'category.html', context)


def mother_register(request):
    if request.method == 'POST':
        mother_register_form = MotherRegisterForm(request.POST, request.FILES)
        if mother_register_form.is_valid():
            mother_register_form.save()

            subject = 'Thank you for registering! Your tickets for you & your family have been confirmed!'
            email = mother_register_form.cleaned_data.get('email')

            html_template = get_template("tickets-email/mothers.html")
            context = {
                'email': email,
            }
            html_content = html_template.render(context)
            email_message = EmailMultiAlternatives(subject, html_content, settings.DEFAULT_FROM_EMAIL, [email])
            email_message.attach_alternative(html_content, "text/html")
            EmailThread(email_message).start()

            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': mother_register_form.errors})
    else:
        mother_register_form = MotherRegisterForm()

    context = {
        'mother_register_form': mother_register_form,
    }
    return render(request, 'registrations/mothers/mother_register.html', context)


def referral_mother_register(request, slug):
    referrals = Referral.objects.get(slug=slug)
    if request.method == 'POST':
        mother_register_form = MotherRegisterForm(request.POST, request.FILES)
        if mother_register_form.is_valid():
            email = mother_register_form.cleaned_data.get('email')

            # Save the form instance
            mother_form = mother_register_form.save(commit=False)
            mother_form.referrals = referrals
            mother_form.save()

            subject = 'Thank you for registering! Your tickets for you & your family have been confirmed!'
            html_template = get_template("tickets-email/mothers.html")
            context = {
                'email': email,
            }
            html_content = html_template.render(context)
            email_message = EmailMultiAlternatives(subject, html_content, settings.DEFAULT_FROM_EMAIL, [email])
            email_message.attach_alternative(html_content, "text/html")
            EmailThread(email_message).start()

            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': mother_register_form.errors})
    else:
        mother_register_form = MotherRegisterForm()

    context = {
        'mother_register_form': mother_register_form,
        'referrals': referrals,
    }
    return render(request, 'registrations/mothers/referral_mother_register.html', context)

def fancy_register(request):
    if request.method == 'POST':
        fancy_form = FancyRegisterForm(request.POST, request.FILES)
        if fancy_form.is_valid():
            fancy_form.save()
            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': fancy_form.errors})
    else:
        fancy_form = FancyRegisterForm()

    context = {
        'fancy_form':fancy_form,
    }
    return render(request, 'registrations/fancy/fancy_register.html', context)

def referral_fancy_register(request, slug):
    referrals = Referral.objects.get(slug=slug)
    if request.method == 'POST':
        fancy_form = FancyRegisterForm(request.POST, request.FILES)
        if fancy_form.is_valid():
            fancy_form = fancy_form.save(commit=False)
            fancy_form.referrals = referrals
            fancy_form.save()
            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': fancy_form.errors})
    else:
        fancy_form = FancyRegisterForm()

    context = {
        'fancy_form':fancy_form,
        'referrals':referrals,
    }
    return render(request, 'registrations/fancy/referral_fancy_register.html', context)


def drawing_register(request):
    if request.method == 'POST':
        drawing_form = DrawingRegisterForm(request.POST, request.FILES)
        if drawing_form.is_valid():
            drawing_form.save()
            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': drawing_form.errors})
    else:
        drawing_form = DrawingRegisterForm()

    context = {
        'drawing_form':drawing_form,
    }
    return render(request, 'registrations/drawing/drawing_register.html', context)

def referral_drawing_register(request, slug):
    referrals = Referral.objects.get(slug=slug)
    if request.method == 'POST':
        drawing_form = DrawingRegisterForm(request.POST, request.FILES)
        if drawing_form.is_valid():
            drawing_form = drawing_form.save(commit=False)
            drawing_form.referrals = referrals
            drawing_form.save()
            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': drawing_form.errors})
    else:
        drawing_form = DrawingRegisterForm()

    context = {
        'drawing_form':drawing_form,
        'referrals': referrals,
    }
    return render(request, 'registrations/drawing/referral_drawing_register.html', context)




def singing_register(request):
    if request.method == 'POST':
        singing_form = SingingRegisterForm(request.POST, request.FILES)
        if singing_form.is_valid():
            singing_form.save()
            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': singing_form.errors})
    else:
        singing_form = SingingRegisterForm()

    context = {
        'singing_form':singing_form,
    }
    return render(request, 'registrations/singing/singing_register.html', context)



def referral_singing_register(request, slug):
    referrals = Referral.objects.get(slug=slug)
    if request.method == 'POST':
        singing_form = SingingRegisterForm(request.POST, request.FILES)
        if singing_form.is_valid():
            singing_form = singing_form.save(commit=False)
            singing_form.referrals = referrals
            singing_form.save()
            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': singing_form.errors})
    else:
        singing_form = SingingRegisterForm()

    context = {
        'singing_form':singing_form,
        'referrals':referrals,
    }
    return render(request, 'registrations/singing/referral_singing_register.html', context)


def dance_register(request):
    if request.method == 'POST':
        dance_form = DanceRegisterForm(request.POST, request.FILES)
        if dance_form.is_valid():
            dance_form.save()
            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': dance_form.errors})
    else:
        dance_form = DanceRegisterForm()

    context = {
        'dance_form': dance_form,
    }
    return render(request, 'registrations/dance/dance_register.html', context)

def referral_dance_register(request, slug):
    referrals = Referral.objects.get(slug=slug)
    if request.method == 'POST':
        dance_form = DanceRegisterForm(request.POST, request.FILES)
        if dance_form.is_valid():
            dance_form = dance_form.save(commit=False)
            dance_form.referrals = referrals
            dance_form.save()
            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': dance_form.errors})
    else:
        dance_form = DanceRegisterForm()

    context = {
        'dance_form': dance_form,
        'referrals': referrals,
    }
    return render(request, 'registrations/dance/referral_dance_register.html', context)

def tickets(request):
    if request.method == 'POST':
        tickets_form = TicketsForm(request.POST)
        if tickets_form.is_valid():
            tickets_form.save()

            subject = 'Your ticket has been confirmed!'
            email = tickets_form.cleaned_data.get('email')

            html_template = get_template("tickets-email/tickets.html")
            context = {
                'email': email,
            }
            html_content = html_template.render(context)
            email_message = EmailMultiAlternatives(subject, html_content, settings.DEFAULT_FROM_EMAIL, [email])
            email_message.attach_alternative(html_content, "text/html")
            EmailThread(email_message).start()

            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': tickets_form.errors})
    else:
        tickets_form = TicketsForm()

    context = {
        'tickets_form': tickets_form,
    }
    return render(request, 'registrations/tickets/tickets.html', context)

def referral_tickets(request, slug):
    referrals = Referral.objects.get(slug=slug)
    if request.method == 'POST':
        tickets_form = TicketsForm(request.POST)
        if tickets_form.is_valid():
            # Access cleaned_data before saving the form
            email = tickets_form.cleaned_data.get('email')

            # Save the form instance
            tickets_instance = tickets_form.save(commit=False)
            tickets_instance.referrals = referrals
            tickets_instance.save()

            subject = 'Your ticket has been confirmed!'
            html_template = get_template("tickets-email/tickets.html")
            context = {
                'email': email,
            }
            html_content = html_template.render(context)
            email_message = EmailMultiAlternatives(subject, html_content, settings.DEFAULT_FROM_EMAIL, [email])
            email_message.attach_alternative(html_content, "text/html")
            EmailThread(email_message).start()

            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': tickets_form.errors})
    else:
        tickets_form = TicketsForm()

    context = {
        'tickets_form': tickets_form,
        'referrals': referrals,
    }
    return render(request, 'registrations/tickets/referral_tickets.html', context)

def baby_photo_register(request):
    if request.method == 'POST':
        baby_photo_form = BabyPhotoRegisterForm(request.POST, request.FILES)
        if baby_photo_form.is_valid():
            baby_photo_form.save()
            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': baby_photo_form.errors})
    else:
        baby_photo_form = BabyPhotoRegisterForm()

    context = {
        'baby_photo_form': baby_photo_form,
    }
    return render(request, 'registrations/baby-photo/baby_photo_register.html', context)

def referral_baby_photo_register(request, slug):
    referrals = Referral.objects.get(slug=slug)
    if request.method == 'POST':
        baby_photo_form = BabyPhotoRegisterForm(request.POST, request.FILES)
        if baby_photo_form.is_valid():
            baby_photo_form = baby_photo_form.save(commit=False)
            baby_photo_form.referrals = referrals
            baby_photo_form.save()
            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': baby_photo_form.errors})
    else:
        baby_photo_form = BabyPhotoRegisterForm()

    context = {
        'baby_photo_form': baby_photo_form,
        'referrals': referrals,
    }
    return render(request, 'registrations/baby-photo/referral_baby_photo_register.html', context)


def referral_category(request, slug):
    referral = Referral.objects.get(slug=slug)
    context ={
        'referral': referral,
    }
    return render(request, 'referral_category.html', context)


# New Registration Views
def baby_photography_register(request):
    if request.method == 'POST':
        baby_photography_form = BabyPhotographyRegisterForm(request.POST, request.FILES)
        if baby_photography_form.is_valid():
            baby_photography_form.save()

            # Send email
            subject = "Thank you for registering! Your tickets for you & your family have been confirmed!"
            from_email = settings.EMAIL_HOST_USER
            to_email = [baby_photography_form.cleaned_data['email']]

            html_template = get_template('tickets-email/mothers.html')
            html_content = html_template.render({'name': baby_photography_form.cleaned_data['parents_name']})

            email = EmailMultiAlternatives(subject, '', from_email, to_email)
            email.attach_alternative(html_content, "text/html")

            EmailThread(email).start()

            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': baby_photography_form.errors})
    else:
        baby_photography_form = BabyPhotographyRegisterForm()

    context = {
        'baby_photography_form': baby_photography_form,
    }
    return render(request, 'registrations/baby-photography/baby_photography_register.html', context)


def referral_baby_photography_register(request, slug):
    referrals = Referral.objects.get(slug=slug)
    if request.method == 'POST':
        baby_photography_form = BabyPhotographyRegisterForm(request.POST, request.FILES)
        if baby_photography_form.is_valid():
            baby_photography_register_instance = baby_photography_form.save(commit=False)
            baby_photography_register_instance.referrals = referrals
            baby_photography_register_instance.save()

            # Update referral count
            referrals.ref_count += 1
            referrals.save()

            # Send email
            subject = "Thank you for registering! Your tickets for you & your family have been confirmed!"
            from_email = settings.EMAIL_HOST_USER
            to_email = [baby_photography_form.cleaned_data['email']]

            html_template = get_template('tickets-email/mothers.html')
            html_content = html_template.render({'name': baby_photography_form.cleaned_data['parents_name']})

            email = EmailMultiAlternatives(subject, '', from_email, to_email)
            email.attach_alternative(html_content, "text/html")

            EmailThread(email).start()

            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': baby_photography_form.errors})
    else:
        baby_photography_form = BabyPhotographyRegisterForm()

    context = {
        'baby_photography_form': baby_photography_form,
        'referrals': referrals,
    }
    return render(request, 'registrations/baby-photography/referral_baby_photography_register.html', context)


# Canvas of Dream Registration
def canvas_of_dream_register(request):
    if request.method == 'POST':
        canvas_of_dream_form = CanvasOfDreamRegisterForm(request.POST, request.FILES)
        if canvas_of_dream_form.is_valid():
            canvas_of_dream_form.save()

            # Send email
            subject = "Thank you for registering! Your tickets for you & your family have been confirmed!"
            from_email = settings.EMAIL_HOST_USER
            to_email = [canvas_of_dream_form.cleaned_data['email']]

            html_template = get_template('tickets-email/mothers.html')
            html_content = html_template.render({'name': canvas_of_dream_form.cleaned_data['parents_name']})

            email = EmailMultiAlternatives(subject, '', from_email, to_email)
            email.attach_alternative(html_content, "text/html")

            EmailThread(email).start()

            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': canvas_of_dream_form.errors})
    else:
        canvas_of_dream_form = CanvasOfDreamRegisterForm()

    context = {
        'canvas_of_dream_form': canvas_of_dream_form,
    }
    return render(request, 'registrations/canvas-of-dream/canvas_of_dream_register.html', context)


def referral_canvas_of_dream_register(request, slug):
    referrals = Referral.objects.get(slug=slug)
    if request.method == 'POST':
        canvas_of_dream_form = CanvasOfDreamRegisterForm(request.POST, request.FILES)
        if canvas_of_dream_form.is_valid():
            canvas_of_dream_register_instance = canvas_of_dream_form.save(commit=False)
            canvas_of_dream_register_instance.referrals = referrals
            canvas_of_dream_register_instance.save()

            # Update referral count
            referrals.ref_count += 1
            referrals.save()

            # Send email
            subject = "Thank you for registering! Your tickets for you & your family have been confirmed!"
            from_email = settings.EMAIL_HOST_USER
            to_email = [canvas_of_dream_form.cleaned_data['email']]

            html_template = get_template('tickets-email/mothers.html')
            html_content = html_template.render({'name': canvas_of_dream_form.cleaned_data['parents_name']})

            email = EmailMultiAlternatives(subject, '', from_email, to_email)
            email.attach_alternative(html_content, "text/html")

            EmailThread(email).start()

            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': canvas_of_dream_form.errors})
    else:
        canvas_of_dream_form = CanvasOfDreamRegisterForm()

    context = {
        'canvas_of_dream_form': canvas_of_dream_form,
        'referrals': referrals,
    }
    return render(request, 'registrations/canvas-of-dream/referral_canvas_of_dream_register.html', context)


# Card Making Registration
def card_making_register(request):
    if request.method == 'POST':
        card_making_form = CardMakingRegisterForm(request.POST, request.FILES)
        if card_making_form.is_valid():
            card_making_form.save()

            # Send email
            subject = "Thank you for registering! Your tickets for you & your family have been confirmed!"
            from_email = settings.EMAIL_HOST_USER
            to_email = [card_making_form.cleaned_data['email']]

            html_template = get_template('tickets-email/mothers.html')
            html_content = html_template.render({'name': card_making_form.cleaned_data['parents_name']})

            email = EmailMultiAlternatives(subject, '', from_email, to_email)
            email.attach_alternative(html_content, "text/html")

            EmailThread(email).start()

            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': card_making_form.errors})
    else:
        card_making_form = CardMakingRegisterForm()

    context = {
        'card_making_form': card_making_form,
    }
    return render(request, 'registrations/card-making/card_making_register.html', context)


def referral_card_making_register(request, slug):
    referrals = Referral.objects.get(slug=slug)
    if request.method == 'POST':
        card_making_form = CardMakingRegisterForm(request.POST, request.FILES)
        if card_making_form.is_valid():
            card_making_register_instance = card_making_form.save(commit=False)
            card_making_register_instance.referrals = referrals
            card_making_register_instance.save()

            # Update referral count
            referrals.ref_count += 1
            referrals.save()

            # Send email
            subject = "Thank you for registering! Your tickets for you & your family have been confirmed!"
            from_email = settings.EMAIL_HOST_USER
            to_email = [card_making_form.cleaned_data['email']]

            html_template = get_template('tickets-email/mothers.html')
            html_content = html_template.render({'name': card_making_form.cleaned_data['parents_name']})

            email = EmailMultiAlternatives(subject, '', from_email, to_email)
            email.attach_alternative(html_content, "text/html")

            EmailThread(email).start()

            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': card_making_form.errors})
    else:
        card_making_form = CardMakingRegisterForm()

    context = {
        'card_making_form': card_making_form,
        'referrals': referrals,
    }
    return render(request, 'registrations/card-making/referral_card_making_register.html', context)


# Clay Creations Registration
def clay_creations_register(request):
    if request.method == 'POST':
        clay_creations_form = ClayCreationsRegisterForm(request.POST, request.FILES)
        if clay_creations_form.is_valid():
            clay_creations_form.save()

            # Send email
            subject = "Thank you for registering! Your tickets for you & your family have been confirmed!"
            from_email = settings.EMAIL_HOST_USER
            to_email = [clay_creations_form.cleaned_data['email']]

            html_template = get_template('tickets-email/mothers.html')
            html_content = html_template.render({'name': clay_creations_form.cleaned_data['parents_name']})

            email = EmailMultiAlternatives(subject, '', from_email, to_email)
            email.attach_alternative(html_content, "text/html")

            EmailThread(email).start()

            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': clay_creations_form.errors})
    else:
        clay_creations_form = ClayCreationsRegisterForm()

    context = {
        'clay_creations_form': clay_creations_form,
    }
    return render(request, 'registrations/clay-creations/clay_creations_register.html', context)


def referral_clay_creations_register(request, slug):
    referrals = Referral.objects.get(slug=slug)
    if request.method == 'POST':
        clay_creations_form = ClayCreationsRegisterForm(request.POST, request.FILES)
        if clay_creations_form.is_valid():
            clay_creations_register_instance = clay_creations_form.save(commit=False)
            clay_creations_register_instance.referrals = referrals
            clay_creations_register_instance.save()

            # Update referral count
            referrals.ref_count += 1
            referrals.save()

            # Send email
            subject = "Thank you for registering! Your tickets for you & your family have been confirmed!"
            from_email = settings.EMAIL_HOST_USER
            to_email = [clay_creations_form.cleaned_data['email']]

            html_template = get_template('tickets-email/mothers.html')
            html_content = html_template.render({'name': clay_creations_form.cleaned_data['parents_name']})

            email = EmailMultiAlternatives(subject, '', from_email, to_email)
            email.attach_alternative(html_content, "text/html")

            EmailThread(email).start()

            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': clay_creations_form.errors})
    else:
        clay_creations_form = ClayCreationsRegisterForm()

    context = {
        'clay_creations_form': clay_creations_form,
        'referrals': referrals,
    }
    return render(request, 'registrations/clay-creations/referral_clay_creations_register.html', context)


# Colors In Motion Registration
def colors_in_motion_register(request):
    if request.method == 'POST':
        colors_in_motion_form = ColorsInMotionRegisterForm(request.POST, request.FILES)
        if colors_in_motion_form.is_valid():
            colors_in_motion_form.save()

            # Send email
            subject = "Thank you for registering! Your tickets for you & your family have been confirmed!"
            from_email = settings.EMAIL_HOST_USER
            to_email = [colors_in_motion_form.cleaned_data['email']]

            html_template = get_template('tickets-email/mothers.html')
            html_content = html_template.render({'name': colors_in_motion_form.cleaned_data['parents_name']})

            email = EmailMultiAlternatives(subject, '', from_email, to_email)
            email.attach_alternative(html_content, "text/html")

            EmailThread(email).start()

            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': colors_in_motion_form.errors})
    else:
        colors_in_motion_form = ColorsInMotionRegisterForm()

    context = {
        'colors_in_motion_form': colors_in_motion_form,
    }
    return render(request, 'registrations/colors-in-motion/colors_in_motion_register.html', context)


def referral_colors_in_motion_register(request, slug):
    referrals = Referral.objects.get(slug=slug)
    if request.method == 'POST':
        colors_in_motion_form = ColorsInMotionRegisterForm(request.POST, request.FILES)
        if colors_in_motion_form.is_valid():
            colors_in_motion_register_instance = colors_in_motion_form.save(commit=False)
            colors_in_motion_register_instance.referrals = referrals
            colors_in_motion_register_instance.save()

            # Update referral count
            referrals.ref_count += 1
            referrals.save()

            # Send email
            subject = "Thank you for registering! Your tickets for you & your family have been confirmed!"
            from_email = settings.EMAIL_HOST_USER
            to_email = [colors_in_motion_form.cleaned_data['email']]

            html_template = get_template('tickets-email/mothers.html')
            html_content = html_template.render({'name': colors_in_motion_form.cleaned_data['parents_name']})

            email = EmailMultiAlternatives(subject, '', from_email, to_email)
            email.attach_alternative(html_content, "text/html")

            EmailThread(email).start()

            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': colors_in_motion_form.errors})
    else:
        colors_in_motion_form = ColorsInMotionRegisterForm()

    context = {
        'colors_in_motion_form': colors_in_motion_form,
        'referrals': referrals,
    }
    return render(request, 'registrations/colors-in-motion/referral_colors_in_motion_register.html', context)


# Eco Warrior Registration
def eco_warrior_register(request):
    if request.method == 'POST':
        eco_warrior_form = EcoWarriorRegisterForm(request.POST, request.FILES)
        if eco_warrior_form.is_valid():
            eco_warrior_form.save()

            # Send email
            subject = "Thank you for registering! Your tickets for you & your family have been confirmed!"
            from_email = settings.EMAIL_HOST_USER
            to_email = [eco_warrior_form.cleaned_data['email']]

            html_template = get_template('tickets-email/mothers.html')
            html_content = html_template.render({'name': eco_warrior_form.cleaned_data['parents_name']})

            email = EmailMultiAlternatives(subject, '', from_email, to_email)
            email.attach_alternative(html_content, "text/html")

            EmailThread(email).start()

            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': eco_warrior_form.errors})
    else:
        eco_warrior_form = EcoWarriorRegisterForm()

    context = {
        'eco_warrior_form': eco_warrior_form,
    }
    return render(request, 'registrations/eco-warrior/eco_warrior_register.html', context)


def referral_eco_warrior_register(request, slug):
    referrals = Referral.objects.get(slug=slug)
    if request.method == 'POST':
        eco_warrior_form = EcoWarriorRegisterForm(request.POST, request.FILES)
        if eco_warrior_form.is_valid():
            eco_warrior_register_instance = eco_warrior_form.save(commit=False)
            eco_warrior_register_instance.referrals = referrals
            eco_warrior_register_instance.save()

            # Update referral count
            referrals.ref_count += 1
            referrals.save()

            # Send email
            subject = "Thank you for registering! Your tickets for you & your family have been confirmed!"
            from_email = settings.EMAIL_HOST_USER
            to_email = [eco_warrior_form.cleaned_data['email']]

            html_template = get_template('tickets-email/mothers.html')
            html_content = html_template.render({'name': eco_warrior_form.cleaned_data['parents_name']})

            email = EmailMultiAlternatives(subject, '', from_email, to_email)
            email.attach_alternative(html_content, "text/html")

            EmailThread(email).start()

            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': eco_warrior_form.errors})
    else:
        eco_warrior_form = EcoWarriorRegisterForm()

    context = {
        'eco_warrior_form': eco_warrior_form,
        'referrals': referrals,
    }
    return render(request, 'registrations/eco-warrior/referral_eco_warrior_register.html', context)


# Fancy Dress Registration
def fancy_dress_register(request):
    if request.method == 'POST':
        fancy_dress_form = FancyDressRegisterForm(request.POST, request.FILES)
        if fancy_dress_form.is_valid():
            fancy_dress_form.save()

            # Send email
            subject = "Thank you for registering! Your tickets for you & your family have been confirmed!"
            from_email = settings.EMAIL_HOST_USER
            to_email = [fancy_dress_form.cleaned_data['email']]

            html_template = get_template('tickets-email/mothers.html')
            html_content = html_template.render({'name': fancy_dress_form.cleaned_data['parents_name']})

            email = EmailMultiAlternatives(subject, '', from_email, to_email)
            email.attach_alternative(html_content, "text/html")

            EmailThread(email).start()

            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': fancy_dress_form.errors})
    else:
        fancy_dress_form = FancyDressRegisterForm()

    context = {
        'fancy_dress_form': fancy_dress_form,
    }
    return render(request, 'registrations/fancy-dress/fancy_dress_register.html', context)


def referral_fancy_dress_register(request, slug):
    referrals = Referral.objects.get(slug=slug)
    if request.method == 'POST':
        fancy_dress_form = FancyDressRegisterForm(request.POST, request.FILES)
        if fancy_dress_form.is_valid():
            fancy_dress_register_instance = fancy_dress_form.save(commit=False)
            fancy_dress_register_instance.referrals = referrals
            fancy_dress_register_instance.save()

            # Update referral count
            referrals.ref_count += 1
            referrals.save()

            # Send email
            subject = "Thank you for registering! Your tickets for you & your family have been confirmed!"
            from_email = settings.EMAIL_HOST_USER
            to_email = [fancy_dress_form.cleaned_data['email']]

            html_template = get_template('tickets-email/mothers.html')
            html_content = html_template.render({'name': fancy_dress_form.cleaned_data['parents_name']})

            email = EmailMultiAlternatives(subject, '', from_email, to_email)
            email.attach_alternative(html_content, "text/html")

            EmailThread(email).start()

            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': fancy_dress_form.errors})
    else:
        fancy_dress_form = FancyDressRegisterForm()

    context = {
        'fancy_dress_form': fancy_dress_form,
        'referrals': referrals,
    }
    return render(request, 'registrations/fancy-dress/referral_fancy_dress_register.html', context)


# Twinning With Mom Registration
def twinning_with_mom_register(request):
    if request.method == 'POST':
        twinning_with_mom_form = TwinningWithMomRegisterForm(request.POST, request.FILES)
        if twinning_with_mom_form.is_valid():
            twinning_with_mom_form.save()

            # Send email
            subject = "Thank you for registering! Your tickets for you & your family have been confirmed!"
            from_email = settings.EMAIL_HOST_USER
            to_email = [twinning_with_mom_form.cleaned_data['email']]

            html_template = get_template('tickets-email/mothers.html')
            html_content = html_template.render({'name': twinning_with_mom_form.cleaned_data['parents_name']})

            email = EmailMultiAlternatives(subject, '', from_email, to_email)
            email.attach_alternative(html_content, "text/html")

            EmailThread(email).start()

            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': twinning_with_mom_form.errors})
    else:
        twinning_with_mom_form = TwinningWithMomRegisterForm()

    context = {
        'twinning_with_mom_form': twinning_with_mom_form,
    }
    return render(request, 'registrations/twinning-with-mom/twinning_with_mom_register.html', context)


def referral_twinning_with_mom_register(request, slug):
    referrals = Referral.objects.get(slug=slug)
    if request.method == 'POST':
        twinning_with_mom_form = TwinningWithMomRegisterForm(request.POST, request.FILES)
        if twinning_with_mom_form.is_valid():
            twinning_with_mom_register_instance = twinning_with_mom_form.save(commit=False)
            twinning_with_mom_register_instance.referrals = referrals
            twinning_with_mom_register_instance.save()

            # Update referral count
            referrals.ref_count += 1
            referrals.save()

            # Send email
            subject = "Thank you for registering! Your tickets for you & your family have been confirmed!"
            from_email = settings.EMAIL_HOST_USER
            to_email = [twinning_with_mom_form.cleaned_data['email']]

            html_template = get_template('tickets-email/mothers.html')
            html_content = html_template.render({'name': twinning_with_mom_form.cleaned_data['parents_name']})

            email = EmailMultiAlternatives(subject, '', from_email, to_email)
            email.attach_alternative(html_content, "text/html")

            EmailThread(email).start()

            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': twinning_with_mom_form.errors})
    else:
        twinning_with_mom_form = TwinningWithMomRegisterForm()

    context = {
        'twinning_with_mom_form': twinning_with_mom_form,
        'referrals': referrals,
    }
    return render(request, 'registrations/twinning-with-mom/referral_twinning_with_mom_register.html', context)


# Waste To Wow Registration
def waste_to_wow_register(request):
    if request.method == 'POST':
        waste_to_wow_form = WasteToWowRegisterForm(request.POST, request.FILES)
        if waste_to_wow_form.is_valid():
            waste_to_wow_form.save()

            # Send email
            subject = "Thank you for registering! Your tickets for you & your family have been confirmed!"
            from_email = settings.EMAIL_HOST_USER
            to_email = [waste_to_wow_form.cleaned_data['email']]

            html_template = get_template('tickets-email/mothers.html')
            html_content = html_template.render({'name': waste_to_wow_form.cleaned_data['parents_name']})

            email = EmailMultiAlternatives(subject, '', from_email, to_email)
            email.attach_alternative(html_content, "text/html")

            EmailThread(email).start()

            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': waste_to_wow_form.errors})
    else:
        waste_to_wow_form = WasteToWowRegisterForm()

    context = {
        'waste_to_wow_form': waste_to_wow_form,
    }
    return render(request, 'registrations/waste-to-wow/waste_to_wow_register.html', context)


def referral_waste_to_wow_register(request, slug):
    referrals = Referral.objects.get(slug=slug)
    if request.method == 'POST':
        waste_to_wow_form = WasteToWowRegisterForm(request.POST, request.FILES)
        if waste_to_wow_form.is_valid():
            waste_to_wow_register_instance = waste_to_wow_form.save(commit=False)
            waste_to_wow_register_instance.referrals = referrals
            waste_to_wow_register_instance.save()

            # Update referral count
            referrals.ref_count += 1
            referrals.save()

            # Send email
            subject = "Thank you for registering! Your tickets for you & your family have been confirmed!"
            from_email = settings.EMAIL_HOST_USER
            to_email = [waste_to_wow_form.cleaned_data['email']]

            html_template = get_template('tickets-email/mothers.html')
            html_content = html_template.render({'name': waste_to_wow_form.cleaned_data['parents_name']})

            email = EmailMultiAlternatives(subject, '', from_email, to_email)
            email.attach_alternative(html_content, "text/html")

            EmailThread(email).start()

            return JsonResponse({'success': True})
        else:
            return JsonResponse({'success': False, 'errors': waste_to_wow_form.errors})
    else:
        waste_to_wow_form = WasteToWowRegisterForm()

    context = {
        'waste_to_wow_form': waste_to_wow_form,
        'referrals': referrals,
    }
    return render(request, 'registrations/waste-to-wow/referral_waste_to_wow_register.html', context)
