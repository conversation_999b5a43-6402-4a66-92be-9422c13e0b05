from django.contrib import admin
from import_export.admin import ImportExportMixin
from .models import (
    Category, Banner, Partners, Referral, MotherRegister, FancyRegister,
    DrawingRegister, SingingRegister, DanceRegister, Tickets, BabyPhotoRegister,
    BabyPhotographyRegister, CanvasOfDreamRegister, CardMakingRegister, ClayCreationsRegister,
    ColorsInMotionRegister, EcoWarriorRegister, FancyDressRegister, TwinningWithMomRegister, WasteToWowRegister
)

# Register your models here.

class CategoryAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('order_no', 'title', 'image', 'link')
    search_fields = ('title',)


class BannerAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('order_no', 'name', 'image', 'mobile_image', 'link')
    search_fields = ('name',)


class PartnersAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('order_no', 'name', 'image', 'link')
    search_fields = ('name',)


class ReferralAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('name', 'phone', 'email', 'nationality', 'ref_count', 'slug')
    search_fields = ('name', 'phone', 'email')


class MotherRegisterAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('name', 'phone', 'email', 'photo', 'nationality', 'age_group', 'emirates', 'referrals', 'status')
    search_fields = ('name', 'email', 'phone')


class FancyRegisterAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('parents_name', 'phone','photo', 'email', 'child_name', 'child_age', 'nationality', 'referrals', 'status')
    search_fields = ('parents_name', 'phone', 'email', 'child_name')


class DrawingRegisterAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('parents_name', 'phone', 'photo', 'email', 'child_name', 'child_age', 'nationality', 'referrals', 'status')
    search_fields = ('parents_name', 'phone', 'email', 'child_name')


class SingingRegisterAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('parents_name', 'phone', 'photo', 'email', 'child_name', 'child_age', 'nationality', 'referrals', 'status')
    search_fields = ('parents_name', 'phone', 'email', 'child_name')


class DanceRegisterAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('parents_name', 'phone', 'photo', 'email', 'child_name', 'child_age', 'nationality', 'referrals', 'status')
    search_fields = ('parents_name', 'phone', 'email', 'child_name')


class TicketsRegisterAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('name', 'phone', 'email', 'nationality', 'members', 'referrals', 'status')
    search_fields = ('name', 'email', 'phone', 'emirates')


class BabyPhotoRegisterAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('parents_name', 'phone', 'photo', 'email', 'child_name', 'child_age', 'nationality', 'referrals', 'status')
    search_fields = ('parents_name', 'phone', 'email', 'child_name')


# New Registration Admin Classes
class BabyPhotographyRegisterAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('parents_name', 'phone', 'photo', 'email', 'child_name', 'child_age', 'nationality', 'referrals', 'status')
    search_fields = ('parents_name', 'phone', 'email', 'child_name')


class CanvasOfDreamRegisterAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('parents_name', 'phone', 'photo', 'email', 'child_name', 'child_age', 'nationality', 'referrals', 'status')
    search_fields = ('parents_name', 'phone', 'email', 'child_name')


class CardMakingRegisterAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('parents_name', 'phone', 'photo', 'email', 'child_name', 'child_age', 'nationality', 'referrals', 'status')
    search_fields = ('parents_name', 'phone', 'email', 'child_name')


class ClayCreationsRegisterAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('parents_name', 'phone', 'photo', 'email', 'child_name', 'child_age', 'nationality', 'referrals', 'status')
    search_fields = ('parents_name', 'phone', 'email', 'child_name')


class ColorsInMotionRegisterAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('parents_name', 'phone', 'photo', 'email', 'child_name', 'child_age', 'nationality', 'referrals', 'status')
    search_fields = ('parents_name', 'phone', 'email', 'child_name')


class EcoWarriorRegisterAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('parents_name', 'phone', 'photo', 'email', 'child_name', 'child_age', 'nationality', 'referrals', 'status')
    search_fields = ('parents_name', 'phone', 'email', 'child_name')


class FancyDressRegisterAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('parents_name', 'phone', 'photo', 'email', 'child_name', 'child_age', 'nationality', 'referrals', 'status')
    search_fields = ('parents_name', 'phone', 'email', 'child_name')


class TwinningWithMomRegisterAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('parents_name', 'phone', 'photo', 'email', 'child_name', 'child_age', 'nationality', 'referrals', 'status')
    search_fields = ('parents_name', 'phone', 'email', 'child_name')


class WasteToWowRegisterAdmin(ImportExportMixin, admin.ModelAdmin):
    list_display = ('parents_name', 'phone', 'photo', 'email', 'child_name', 'child_age', 'nationality', 'referrals', 'status')
    search_fields = ('parents_name', 'phone', 'email', 'child_name')



# Register all the admin classes
admin.site.register(Category, CategoryAdmin)
admin.site.register(Banner, BannerAdmin)
admin.site.register(Partners, PartnersAdmin)
admin.site.register(Referral, ReferralAdmin)
admin.site.register(MotherRegister, MotherRegisterAdmin)
admin.site.register(FancyRegister, FancyRegisterAdmin)
admin.site.register(DrawingRegister, DrawingRegisterAdmin)
admin.site.register(SingingRegister, SingingRegisterAdmin)
admin.site.register(DanceRegister, DanceRegisterAdmin)
admin.site.register(Tickets, TicketsRegisterAdmin)
admin.site.register(BabyPhotoRegister, BabyPhotoRegisterAdmin)

# Register new registration models
admin.site.register(BabyPhotographyRegister, BabyPhotographyRegisterAdmin)
admin.site.register(CanvasOfDreamRegister, CanvasOfDreamRegisterAdmin)
admin.site.register(CardMakingRegister, CardMakingRegisterAdmin)
admin.site.register(ClayCreationsRegister, ClayCreationsRegisterAdmin)
admin.site.register(ColorsInMotionRegister, ColorsInMotionRegisterAdmin)
admin.site.register(EcoWarriorRegister, EcoWarriorRegisterAdmin)
admin.site.register(FancyDressRegister, FancyDressRegisterAdmin)
admin.site.register(TwinningWithMomRegister, TwinningWithMomRegisterAdmin)
admin.site.register(WasteToWowRegister, WasteToWowRegisterAdmin)
