from ckeditor.fields import Rich<PERSON>ext<PERSON>ield
from django.db import models
from django.utils.text import slugify


# Create your models here.

class Category(models.Model):
    order_no = models.IntegerField()
    title = models.CharField(max_length=255)
    image = models.FileField(upload_to='Category')
    link = models.URLField()
    created_date = models.DateField(auto_now=True)
    updated_date = models.DateField(auto_now_add=True)

    def __str__(self):
        return self.title

class Banner(models.Model):
    order_no = models.IntegerField()
    name = models.CharField(max_length=255)
    image = models.FileField(upload_to='Banner')
    mobile_image = models.FileField(upload_to='Mobile Banner')
    link = models.URLField(null=True, blank=True)

    def __str__(self):
        return self.name

class Partners(models.Model):
    order_no = models.IntegerField()
    name = models.CharField(max_length=255)
    image = models.FileField(upload_to='Partners')
    link = models.U<PERSON><PERSON>ield(null=True, blank=True)

    def __str__(self):
        return self.name


class Referral(models.Model):
    name = models.CharField(max_length=255, null=True, blank=True)
    phone = models.CharField(max_length=255, null=True, blank=True)
    email = models.EmailField(null=True, blank=True)
    nationality = models.CharField(max_length=255, null=True, blank=True)
    ref_count = models.CharField(max_length=255, null=True, blank=True)
    slug = models.SlugField(max_length=200, unique=True, blank=True)  # Allow blank for auto-generation

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)



class MotherRegister(models.Model):
    CHOICES_STATIC = [
        ('Approved', 'Approved'),
        ('Pending', 'Pending'),
    ]
    MOM_STATUS = [
        ('Expecting Mom', 'Expecting Mom'),
        ('0-3', '0-3'),
        ('3-12', '3-12'),
        ('Others', 'Others'),
    ]
    referrals = models.ForeignKey(Referral, on_delete=models.SET_NULL, blank=True, null=True)
    photo = models.FileField(upload_to='Mother Register', null=True, blank=True)
    name = models.CharField(max_length=1025, null=True, blank=True)
    phone = models.CharField(max_length=1025, null=True, blank=True)
    email = models.CharField(max_length=1025, null=True, blank=True)
    nationality = models.CharField(max_length=1025, null=True, blank=True)
    age_group = models.CharField(max_length=1025, choices=MOM_STATUS, null=True, blank=True)
    emirates = models.CharField(max_length=1025, null=True, blank=True)
    insta_link = models.CharField(max_length=1025, null=True, blank=True)
    created_date = models.DateField(auto_now=True, null=True, blank=True)
    updated_date = models.DateField(auto_now_add=True, null=True, blank=True)
    status = models.CharField(max_length=1025, choices=CHOICES_STATIC, null=True, blank=True)

    def __str__(self):
        return self.name


class FancyRegister(models.Model):
    CHOICES_STATIC = [
        ('Approved', 'Approved'),
        ('Pending', 'Pending'),
    ]
    referrals = models.ForeignKey(Referral, on_delete=models.SET_NULL, blank=True, null=True)
    parents_name = models.CharField(max_length=1025, null=True, blank=True)
    phone = models.CharField(max_length=1025, null=True, blank=True)
    email = models.CharField(max_length=1025, null=True, blank=True)
    child_name = models.CharField(max_length=1025, null=True, blank=True)
    child_age = models.CharField(max_length=1025, null=True, blank=True)
    nationality = models.CharField(max_length=1025, null=True, blank=True)
    about = models.TextField(null=True, blank=True)
    photo = models.FileField(upload_to='Fancy Register', null=True, blank=True)
    insta_link = models.CharField(max_length=1025, null=True, blank=True)
    created_date = models.DateField(auto_now=True, null=True, blank=True)
    updated_date = models.DateField(auto_now_add=True, null=True, blank=True)
    status = models.CharField(max_length=1025, choices=CHOICES_STATIC, null=True, blank=True)

    def __str__(self):
        return self.parents_name


class DrawingRegister(models.Model):
    CHOICES_STATIC = [
        ('Approved', 'Approved'),
        ('Pending', 'Pending'),
    ]
    referrals = models.ForeignKey(Referral, on_delete=models.SET_NULL, blank=True, null=True)
    parents_name = models.CharField(max_length=1025, null=True, blank=True)
    phone = models.CharField(max_length=1025, null=True, blank=True)
    email = models.CharField(max_length=1025, null=True, blank=True)
    child_name = models.CharField(max_length=1025, null=True, blank=True)
    child_age = models.CharField(max_length=1025, null=True, blank=True)
    nationality = models.CharField(max_length=1025, null=True, blank=True)
    about = models.TextField(null=True, blank=True)
    photo = models.FileField(upload_to='Drawing Register', null=True, blank=True)
    insta_link = models.CharField(max_length=1025, null=True, blank=True)
    created_date = models.DateField(auto_now=True, null=True, blank=True)
    updated_date = models.DateField(auto_now_add=True, null=True, blank=True)
    status = models.CharField(max_length=1025, choices=CHOICES_STATIC, null=True, blank=True)

    def __str__(self):
        return self.parents_name


class SingingRegister(models.Model):
    CHOICES_STATIC = [
        ('Approved', 'Approved'),
        ('Pending', 'Pending'),
    ]
    referrals = models.ForeignKey(Referral, on_delete=models.SET_NULL, blank=True, null=True)
    parents_name = models.CharField(max_length=1025, null=True, blank=True)
    phone = models.CharField(max_length=1025, null=True, blank=True)
    email = models.CharField(max_length=1025, null=True, blank=True)
    child_name = models.CharField(max_length=1025, null=True, blank=True)
    child_age = models.CharField(max_length=1025, null=True, blank=True)
    nationality = models.CharField(max_length=1025, null=True, blank=True)
    about = models.TextField(null=True, blank=True)
    photo = models.FileField(upload_to='Singing Register', null=True, blank=True)
    insta_link = models.CharField(max_length=1025, null=True, blank=True)
    created_date = models.DateField(auto_now=True, null=True, blank=True)
    updated_date = models.DateField(auto_now_add=True, null=True, blank=True)
    status = models.CharField(max_length=1025, choices=CHOICES_STATIC, null=True, blank=True)

    def __str__(self):
        return self.parents_name

class DanceRegister(models.Model):
    CHOICES_STATIC = [
        ('Approved', 'Approved'),
        ('Pending', 'Pending'),
    ]
    referrals = models.ForeignKey(Referral, on_delete=models.SET_NULL, blank=True, null=True)
    parents_name = models.CharField(max_length=1025, null=True, blank=True)
    phone = models.CharField(max_length=1025, null=True, blank=True)
    email = models.CharField(max_length=1025, null=True, blank=True)
    child_name = models.CharField(max_length=1025, null=True, blank=True)
    child_age = models.CharField(max_length=1025, null=True, blank=True)
    nationality = models.CharField(max_length=1025, null=True, blank=True)
    about = models.TextField(null=True, blank=True)
    photo = models.FileField(upload_to='Dance Register', null=True, blank=True)
    insta_link = models.CharField(max_length=1025, null=True, blank=True)
    created_date = models.DateField(auto_now=True, null=True, blank=True)
    updated_date = models.DateField(auto_now_add=True, null=True, blank=True)
    status = models.CharField(max_length=1025, choices=CHOICES_STATIC, null=True, blank=True)

    def __str__(self):
        return self.parents_name


class Tickets(models.Model):
    CHOICES_STATIC = [
        ('Approved', 'Approved'),
        ('Pending', 'Pending'),
    ]
    referrals = models.ForeignKey(Referral, on_delete=models.SET_NULL, blank=True, null=True)
    name = models.CharField(max_length=1025, null=True, blank=True)
    phone = models.CharField(max_length=1025, null=True, blank=True)
    email = models.CharField(max_length=1025, null=True, blank=True)
    nationality = models.CharField(max_length=1025, null=True, blank=True)
    emirates = models.CharField(max_length=1025, null=True, blank=True)
    members = models.CharField(max_length=1025, null=True, blank=True)
    created_date = models.DateField(auto_now=True, null=True, blank=True)
    updated_date = models.DateField(auto_now_add=True, null=True, blank=True)
    status = models.CharField(max_length=1025, choices=CHOICES_STATIC, null=True, blank=True)

    def __str__(self):
        return self.name


class BabyPhotoRegister(models.Model):
    CHOICES_STATIC = [
        ('Approved', 'Approved'),
        ('Pending', 'Pending'),
    ]
    referrals = models.ForeignKey(Referral, on_delete=models.SET_NULL, blank=True, null=True)
    parents_name = models.CharField(max_length=1025, null=True, blank=True)
    phone = models.CharField(max_length=1025, null=True, blank=True)
    email = models.CharField(max_length=1025, null=True, blank=True)
    child_name = models.CharField(max_length=1025, null=True, blank=True)
    child_age = models.CharField(max_length=1025, null=True, blank=True)
    nationality = models.CharField(max_length=1025, null=True, blank=True)
    about = models.TextField(null=True, blank=True)
    photo = models.FileField(upload_to='Baby Photo Register', null=True, blank=True)
    insta_link = models.CharField(max_length=255, null=True, blank=True)
    created_date = models.DateField(auto_now=True, null=True, blank=True)
    updated_date = models.DateField(auto_now_add=True, null=True, blank=True)
    status = models.CharField(max_length=1025, choices=CHOICES_STATIC, null=True, blank=True)

    def __str__(self):
        return self.parents_name


# New Registration Models
class BabyPhotographyRegister(models.Model):
    CHOICES_STATIC = [
        ('Pending', 'Pending'),
        ('Approved', 'Approved'),
        ('Rejected', 'Rejected'),
    ]
    referrals = models.ForeignKey(Referral, on_delete=models.SET_NULL, blank=True, null=True)
    parents_name = models.CharField(max_length=1025, null=True, blank=True)
    phone = models.CharField(max_length=1025, null=True, blank=True)
    email = models.CharField(max_length=1025, null=True, blank=True)
    child_name = models.CharField(max_length=1025, null=True, blank=True)
    child_age = models.CharField(max_length=1025, null=True, blank=True)
    nationality = models.CharField(max_length=1025, null=True, blank=True)
    about = models.TextField(null=True, blank=True)
    photo = models.FileField(upload_to='Baby Photography Register', null=True, blank=True)
    insta_link = models.CharField(max_length=255, null=True, blank=True)
    created_date = models.DateField(auto_now=True, null=True, blank=True)
    updated_date = models.DateField(auto_now_add=True, null=True, blank=True)
    status = models.CharField(max_length=1025, choices=CHOICES_STATIC, null=True, blank=True)

    def __str__(self):
        return self.parents_name


class CanvasOfDreamRegister(models.Model):
    CHOICES_STATIC = [
        ('Pending', 'Pending'),
        ('Approved', 'Approved'),
        ('Rejected', 'Rejected'),
    ]
    referrals = models.ForeignKey(Referral, on_delete=models.SET_NULL, blank=True, null=True)
    parents_name = models.CharField(max_length=1025, null=True, blank=True)
    phone = models.CharField(max_length=1025, null=True, blank=True)
    email = models.CharField(max_length=1025, null=True, blank=True)
    child_name = models.CharField(max_length=1025, null=True, blank=True)
    child_age = models.CharField(max_length=1025, null=True, blank=True)
    nationality = models.CharField(max_length=1025, null=True, blank=True)
    about = models.TextField(null=True, blank=True)
    photo = models.FileField(upload_to='Canvas Of Dream Register', null=True, blank=True)
    insta_link = models.CharField(max_length=255, null=True, blank=True)
    created_date = models.DateField(auto_now=True, null=True, blank=True)
    updated_date = models.DateField(auto_now_add=True, null=True, blank=True)
    status = models.CharField(max_length=1025, choices=CHOICES_STATIC, null=True, blank=True)

    def __str__(self):
        return self.parents_name


class CardMakingRegister(models.Model):
    CHOICES_STATIC = [
        ('Pending', 'Pending'),
        ('Approved', 'Approved'),
        ('Rejected', 'Rejected'),
    ]
    referrals = models.ForeignKey(Referral, on_delete=models.SET_NULL, blank=True, null=True)
    parents_name = models.CharField(max_length=1025, null=True, blank=True)
    phone = models.CharField(max_length=1025, null=True, blank=True)
    email = models.CharField(max_length=1025, null=True, blank=True)
    child_name = models.CharField(max_length=1025, null=True, blank=True)
    child_age = models.CharField(max_length=1025, null=True, blank=True)
    nationality = models.CharField(max_length=1025, null=True, blank=True)
    about = models.TextField(null=True, blank=True)
    photo = models.FileField(upload_to='Card Making Register', null=True, blank=True)
    insta_link = models.CharField(max_length=255, null=True, blank=True)
    created_date = models.DateField(auto_now=True, null=True, blank=True)
    updated_date = models.DateField(auto_now_add=True, null=True, blank=True)
    status = models.CharField(max_length=1025, choices=CHOICES_STATIC, null=True, blank=True)

    def __str__(self):
        return self.parents_name


class ClayCreationsRegister(models.Model):
    CHOICES_STATIC = [
        ('Pending', 'Pending'),
        ('Approved', 'Approved'),
        ('Rejected', 'Rejected'),
    ]
    referrals = models.ForeignKey(Referral, on_delete=models.SET_NULL, blank=True, null=True)
    parents_name = models.CharField(max_length=1025, null=True, blank=True)
    phone = models.CharField(max_length=1025, null=True, blank=True)
    email = models.CharField(max_length=1025, null=True, blank=True)
    child_name = models.CharField(max_length=1025, null=True, blank=True)
    child_age = models.CharField(max_length=1025, null=True, blank=True)
    nationality = models.CharField(max_length=1025, null=True, blank=True)
    about = models.TextField(null=True, blank=True)
    photo = models.FileField(upload_to='Clay Creations Register', null=True, blank=True)
    insta_link = models.CharField(max_length=255, null=True, blank=True)
    created_date = models.DateField(auto_now=True, null=True, blank=True)
    updated_date = models.DateField(auto_now_add=True, null=True, blank=True)
    status = models.CharField(max_length=1025, choices=CHOICES_STATIC, null=True, blank=True)

    def __str__(self):
        return self.parents_name


class ColorsInMotionRegister(models.Model):
    CHOICES_STATIC = [
        ('Pending', 'Pending'),
        ('Approved', 'Approved'),
        ('Rejected', 'Rejected'),
    ]
    referrals = models.ForeignKey(Referral, on_delete=models.SET_NULL, blank=True, null=True)
    parents_name = models.CharField(max_length=1025, null=True, blank=True)
    phone = models.CharField(max_length=1025, null=True, blank=True)
    email = models.CharField(max_length=1025, null=True, blank=True)
    child_name = models.CharField(max_length=1025, null=True, blank=True)
    child_age = models.CharField(max_length=1025, null=True, blank=True)
    nationality = models.CharField(max_length=1025, null=True, blank=True)
    about = models.TextField(null=True, blank=True)
    photo = models.FileField(upload_to='Colors In Motion Register', null=True, blank=True)
    insta_link = models.CharField(max_length=255, null=True, blank=True)
    created_date = models.DateField(auto_now=True, null=True, blank=True)
    updated_date = models.DateField(auto_now_add=True, null=True, blank=True)
    status = models.CharField(max_length=1025, choices=CHOICES_STATIC, null=True, blank=True)

    def __str__(self):
        return self.parents_name


class EcoWarriorRegister(models.Model):
    CHOICES_STATIC = [
        ('Pending', 'Pending'),
        ('Approved', 'Approved'),
        ('Rejected', 'Rejected'),
    ]
    referrals = models.ForeignKey(Referral, on_delete=models.SET_NULL, blank=True, null=True)
    parents_name = models.CharField(max_length=1025, null=True, blank=True)
    phone = models.CharField(max_length=1025, null=True, blank=True)
    email = models.CharField(max_length=1025, null=True, blank=True)
    child_name = models.CharField(max_length=1025, null=True, blank=True)
    child_age = models.CharField(max_length=1025, null=True, blank=True)
    nationality = models.CharField(max_length=1025, null=True, blank=True)
    about = models.TextField(null=True, blank=True)
    photo = models.FileField(upload_to='Eco Warrior Register', null=True, blank=True)
    insta_link = models.CharField(max_length=255, null=True, blank=True)
    created_date = models.DateField(auto_now=True, null=True, blank=True)
    updated_date = models.DateField(auto_now_add=True, null=True, blank=True)
    status = models.CharField(max_length=1025, choices=CHOICES_STATIC, null=True, blank=True)

    def __str__(self):
        return self.parents_name


class FancyDressRegister(models.Model):
    CHOICES_STATIC = [
        ('Pending', 'Pending'),
        ('Approved', 'Approved'),
        ('Rejected', 'Rejected'),
    ]
    referrals = models.ForeignKey(Referral, on_delete=models.SET_NULL, blank=True, null=True)
    parents_name = models.CharField(max_length=1025, null=True, blank=True)
    phone = models.CharField(max_length=1025, null=True, blank=True)
    email = models.CharField(max_length=1025, null=True, blank=True)
    child_name = models.CharField(max_length=1025, null=True, blank=True)
    child_age = models.CharField(max_length=1025, null=True, blank=True)
    nationality = models.CharField(max_length=1025, null=True, blank=True)
    about = models.TextField(null=True, blank=True)
    photo = models.FileField(upload_to='Fancy Dress Register', null=True, blank=True)
    insta_link = models.CharField(max_length=255, null=True, blank=True)
    created_date = models.DateField(auto_now=True, null=True, blank=True)
    updated_date = models.DateField(auto_now_add=True, null=True, blank=True)
    status = models.CharField(max_length=1025, choices=CHOICES_STATIC, null=True, blank=True)

    def __str__(self):
        return self.parents_name


class TwinningWithMomRegister(models.Model):
    CHOICES_STATIC = [
        ('Pending', 'Pending'),
        ('Approved', 'Approved'),
        ('Rejected', 'Rejected'),
    ]
    referrals = models.ForeignKey(Referral, on_delete=models.SET_NULL, blank=True, null=True)
    parents_name = models.CharField(max_length=1025, null=True, blank=True)
    phone = models.CharField(max_length=1025, null=True, blank=True)
    email = models.CharField(max_length=1025, null=True, blank=True)
    child_name = models.CharField(max_length=1025, null=True, blank=True)
    child_age = models.CharField(max_length=1025, null=True, blank=True)
    nationality = models.CharField(max_length=1025, null=True, blank=True)
    about = models.TextField(null=True, blank=True)
    photo = models.FileField(upload_to='Twinning With Mom Register', null=True, blank=True)
    insta_link = models.CharField(max_length=255, null=True, blank=True)
    created_date = models.DateField(auto_now=True, null=True, blank=True)
    updated_date = models.DateField(auto_now_add=True, null=True, blank=True)
    status = models.CharField(max_length=1025, choices=CHOICES_STATIC, null=True, blank=True)

    def __str__(self):
        return self.parents_name


class WasteToWowRegister(models.Model):
    CHOICES_STATIC = [
        ('Pending', 'Pending'),
        ('Approved', 'Approved'),
        ('Rejected', 'Rejected'),
    ]
    referrals = models.ForeignKey(Referral, on_delete=models.SET_NULL, blank=True, null=True)
    parents_name = models.CharField(max_length=1025, null=True, blank=True)
    phone = models.CharField(max_length=1025, null=True, blank=True)
    email = models.CharField(max_length=1025, null=True, blank=True)
    child_name = models.CharField(max_length=1025, null=True, blank=True)
    child_age = models.CharField(max_length=1025, null=True, blank=True)
    nationality = models.CharField(max_length=1025, null=True, blank=True)
    about = models.TextField(null=True, blank=True)
    photo = models.FileField(upload_to='Waste To Wow Register', null=True, blank=True)
    insta_link = models.CharField(max_length=255, null=True, blank=True)
    created_date = models.DateField(auto_now=True, null=True, blank=True)
    updated_date = models.DateField(auto_now_add=True, null=True, blank=True)
    status = models.CharField(max_length=1025, choices=CHOICES_STATIC, null=True, blank=True)

    def __str__(self):
        return self.parents_name
