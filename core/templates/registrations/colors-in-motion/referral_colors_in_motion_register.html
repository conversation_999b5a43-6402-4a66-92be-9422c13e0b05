{% extends 'include/base.html' %}

{% block title %}
    <title>WonderMom Fest 2025: Baby Photo Competition ! Register Today</title>
    <meta name="og:title" content="WonderMom Fest 2025: Baby Photo Competition ! Register Today">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
{% endblock %}

{% load static %}
{% block content %}

<section class="section sections-one"
    style="background-image: url('{% static "assets/images/bg/babyphoto.webp" %}');
           background-position: center center;
           background-repeat: no-repeat;
           background-size: cover;">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 align-self-center">
                    <div class="left-text-content">
                        <div class="section-heading">
                            <h6>WonderMom Fest 2025</h6>
                            <h2>WonderMom Fest : Bump to Baby & Back to School Edition</h2>
                        </div>
                      <p style="margin-bottom: 12px;">For any doubts or support, please click here to WhatsApp us or contact us at <a href="https://wa.me/+971 52 937 6415" target="_blank">+971 52 937 6415</a> .
                            <br><br>
                            Get Your Free Tickets Online: <a href="https://fest.thewondermom.club/tickets/{{ referrals.slug }}" target="_blank">Claim Your Tickets</a><br><br>

<b>Our Top Competitions & Registrations</b><br>
Mothers Registrations: <a href="https://fest.thewondermom.club/mother-register/{{ referrals.slug }}" target="_blank">Register Here</a><br>
New Mom Registrations: <a href="https://fest.thewondermom.club/mother-register/{{ referrals.slug }}" target="_blank">Register Here</a><br>
Fancy Dress Competitions: <a href="https://fest.thewondermom.club/fancy-register/{{ referrals.slug }}" target="_blank">Register Here</a><br>
Drawing Competitions: <a href="https://fest.thewondermom.club/drawing-register/{{ referrals.slug }}" target="_blank">Register Here</a><br>
Singing Competitions: <a href="https://fest.thewondermom.club/singing-register/{{ referrals.slug }}" target="_blank">Register Here</a><br>
Dancing Competitions: <a href="https://fest.thewondermom.club/dance-register/{{ referrals.slug }}" target="_blank">Register Here</a><br><br>

<b>Event Details</b><br>

Dates: September 7th & 8th, 2024<br>
Venue: Dubai</p>

                                                <div class="row">
    <div class="col-lg-12">
         <p style="height: 240px; color:black; overflow: auto; background-color:white; padding:10px;">
             <b><u>Guidelines for Baby Photo Competition</u></b><br>
             <br>
             <b><u>Eligibility</u></b><br>Ages: 0-3<br>

             <br><b><u>Theme Choose  Anyone</u> </b> <br>
             1. Cutest Smile: Capture the sweetest smile.<br>
             2. Messy Moments: Photos of your baby during mealtime, playing with paint, etc.<br>
             3. Best Outfit: Baby dressed in their cutest or funniest outfits.<br><br>
             <b><u>Submission Process</u></b><br><br>
             <b><u>Initial Submission</u></b><br>
             Submit a photo.<br>
             Send a clear image via WhatsApp to <a href="https://wa.me/+************" target="_blank">+971 52 937 6415</a>.
             <br><br>
             <b><u>Finalist  Submission</u></b><br>
             All participants’ mothers must arrive at the venue and with a printout of the picture in 4x6 size.<br>
<br>
             <b><u>Judging Criteria</u></b><br><br>

<b><u>Creativity</u></b><br>
Originality<br>
Setting<br><br>

<b><u>Expression</u></b><br>
Emotion<br>
Cuteness<br><br>

<b><u>Winners</u></b><br>
Top 3 participants will be awarded and 2 consolations.<br><br>

<b><u>Prizes</u></b><br>
Awards: Winners’ Hamper, Certificate, and Magazine Feature.<br>
Certificates: All participants will receive a certificate of participation.<br>
         </p>
     </div>

 </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="contact-form">
                        <form id="contact"  method="post" enctype="multipart/form-data" onsubmit="submitForm(event)">
                            {% csrf_token %}
                          <div class="row">
                            <div class="col-lg-12">
                               <center><h6 style="color:blue; margin-bottom:10px;">Referred By {{ referrals.name }}</h6></center>
                                <h4>BABY PHOTO COMPETITION</h4>
                            </div>
                            <div class="col-lg-12 col-sm-12">
                              <fieldset>
                                <input name="parents_name" type="text" id="parents_name" placeholder="Parent's Name *" required="" >
                              </fieldset>
                            </div>

                               <div class="col-lg-12 col-sm-12">
                              <fieldset>
                                <input name="phone" type="text" id="phone" placeholder="Contact Number *" required="" >
                              </fieldset>
                            </div>

                            <div class="col-lg-12 col-sm-12">
                              <fieldset>
                              <input name="email" type="email" id="email" pattern="[^ @]*@[^ @]*" placeholder="Email Address *" required="" >
                            </fieldset>
                            </div>


                            <div class="col-lg-6 col-sm-12">
                              <fieldset>
                                <input name="child_name" type="text" id="child_name" placeholder="Child's Name *" required="" >
                              </fieldset>
                            </div>

                              <div class="col-lg-6 col-sm-12">
                              <fieldset>
                                <input name="child_age" type="text" id="child_age" placeholder="Child's Age *" required="" >
                              </fieldset>
                            </div>


                            <div class="col-lg-12 col-sm-12">
                                <fieldset>
                                  <input name="nationality" type="text" id="nationality" placeholder="Nationality *" required="" >
                                </fieldset>
                              </div>

                              <div class="col-lg-12 col-sm-12">
                              <fieldset>
                            <textarea name="about" rows="3" id="message" placeholder="About Yourself (Child's Description for Social Media Post)" required="" ></textarea>                                                  </fieldset>
                            </div>

                              <div class="col-lg-12 col-sm-12">
                                <fieldset>
                                  <input name="insta_link" type="text" id="insta_link" placeholder="Instagram Link" >
                                </fieldset>
                              </div>

                               <div class="col-lg-12 col-sm-12">
                                <fieldset>
                                    <label>Child's Picture </label>
                                  <input name="photo" type="file" id="photo" placeholder="Child's Picture " style="padding-top:8px;" >
                                </fieldset>
                              </div>


                            <div class="col-lg-12" style="margin-top: 20px;">
                              <fieldset>
                                <button type="submit" id="form-submit" class="main-button-icon" >Submit Now</button>
                              </fieldset>
                            </div>


  <center> <div id="success-message" class="success-message" style="display: none;">
      <div class="col-lg-12">
  Baby Photo Contest submitted successfully!
  </div></div></center>

<div id="error-message" class="error-message" style="display: none;">
    <div class="col-lg-12">
        Baby Photo Contest submission failed. Please check your inputs and try again.
    </div></div>
                          </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>



<script>
       function submitForm(event) {
    event.preventDefault();

    // Create a FormData object from the form element
    var formData = new FormData($('#contact')[0]);

    $.ajax({
         url: '{% url 'referral_baby_photo_register' slug=referrals.slug %}',
        type: 'post',
        data: formData,
        processData: false,  // Prevent jQuery from automatically processing the data
        contentType: false,  // Prevent jQuery from automatically setting the content type
        success: function(response) {
            if (response.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: 'Registration Successful !',
                });

                $('#contact')[0].reset();
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: 'Form submission failed: ' + Object.values(response.errors).join(', '),
                });

                console.log('Form submission failed:', response.errors);
            }
        },
        error: function(xhr, status, error) {
            Swal.fire({
                icon: 'error',
                title: 'AJAX Request Failed',
                text: 'Error: ' + error,
            });

            console.log('AJAX request failed:', error);
        }
    });
}

      </script>

    {% endblock %}