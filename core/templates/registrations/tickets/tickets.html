{% extends 'include/base.html' %}

{% block title %}
    <title>WonderMom Fest 2025: Family Registration ! Register Today</title>
    <meta name="og:title" content="WonderMom Fest 2025: Family Registration ! Register Today">
{% endblock %}

{% load static %}
{% block content %}



    <section class="section sections-one" style="background-image: url('{% static "assets/images/bg/mother_register.webp" %}'); background-position: center center; background-repeat: no-repeat; background-size: cover;">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 align-self-center">
                    <div class="left-text-content">
                        <div class="section-heading">
                            <h6>WonderMom Fest 2025</h6>
                            <h2>WonderMom Fest : Bump to Baby & Back to School Edition</h2>
                        </div>
                      <p style="margin-bottom: 12px;">
<b>Our Top Competitions & Registrations</b><br>
Mothers Registrations: <a href="https://fest.thewondermom.club/mother-register/" target="_blank">Register Here</a><br>
New Mom Registrations: <a href="https://fest.thewondermom.club/mother-register/" target="_blank">Register Here</a><br>
Fancy Dress Competitions: <a href="https://fest.thewondermom.club/fancy-register/" target="_blank">Register Here</a><br>
Singing Competitions: <a href="https://fest.thewondermom.club/singing-register/" target="_blank">Register Here</a><br>
Dancing Competitions: <a href="https://fest.thewondermom.club/dance-register/" target="_blank">Register Here</a><br>
Baby Photo Competitions: <a href="https://fest.thewondermom.club/baby-photo-register/" target="_blank">Register Here</a><br>
Drawing Competitions: <a href="https://fest.thewondermom.club/drawing-register/" target="_blank">Register Here</a><br><br>

<b>Event Details</b><br>

Dates: September 7th & 8th, 2024<br>
Venue: Dubai</p>

<br><br>


                        <div class="row">
                            <div class="col-lg-6">
                                <div class="phone">
                                    <i class="fa fa-phone"></i>
                                    <h4>Phone Numbers</h4>
                                    <span>
                                    <a href="tel:+971545083789">+971 58 631 6069</a><br>
                                    <a href="tel:+971505382042">+971 58 631 6069</a>
                                    </span>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="message">
                                    <i class="fa fa-envelope"></i>
                                    <h4>Emails</h4>
                                    <span><a href="mailto:<EMAIL>"><EMAIL></a>
                                     <a href="mailto:<EMAIL>"><EMAIL></a></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="contact-form">
<form id="contact" method="post" onsubmit="submitForm(event)">
                            {% csrf_token %}
                          <div class="row">
                            <div class="col-lg-12">
                                <h4>Tickets</h4>
                            </div>
                            <div class="col-lg-12 col-sm-12">
                              <fieldset>
                                <input name="name" type="text" id="name" placeholder="Person Name*" required="">
                              </fieldset>
                            </div>

                            <div class="col-lg-12 col-sm-12">
                              <fieldset>
                              <input name="email" type="email" id="email" pattern="[^ @]*@[^ @]*" placeholder="Email Address *" required="">
                            </fieldset>
                            </div>


                            <div class="col-lg-12 col-sm-12">
                              <fieldset>
                                <input name="phone" type="text" id="phone" placeholder="Phone Number *" required="">
                              </fieldset>
                            </div>

                            <div class="col-lg-12 col-sm-12">
                                <fieldset>
                                  <input name="nationality" type="text" id="nationality" placeholder="Nationality *" required="">
                                </fieldset>
                              </div>


                                <div class="col-lg-12 col-sm-12">
                                <fieldset>
                                  <input name="members" type="text" id="members" placeholder="How Many Members Join *" required="">
                                </fieldset>
                              </div>


                            <div class="col-lg-12 col-sm-12">
                                <fieldset>
                                  <input name="emirates" type="text" id="emirates" placeholder="Emirates you stay in *" required=""  >
                                </fieldset>
                              </div>

                            <div class="col-lg-12" style="margin-top: 20px;">
                              <fieldset>
  <button type="submit" id="form-submit" class="main-button-icon">Join Now</button>
                              </fieldset>
                            </div>

  <center> <div id="success-message" class="success-message" style="display: none;">
      <div class="col-lg-12">
  Family Registration submitted successfully!
  </div></div></center>

<div id="error-message" class="error-message" style="display: none;">
    <div class="col-lg-12">
  Family Registration submission failed. Please check your inputs and try again.
    </div></div>
                          </div>
                        </form>

                    </div>
                </div>
            </div>
        </div>
    </section>

<script>
      function submitForm(event) {
        event.preventDefault();

        var formData = $('#contact').serialize();

        $.ajax({
          url: '{% url 'tickets' %}',
          type: 'post',
          data: formData,
          success: function(response) {
            if (response.success) {

              Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'Your tickets for you & your family have been confirmed!',
              });


              $('#contact')[0].reset();


              if (response.data && response.data.name) {
                $('#name').val(response.data.name);
              }
              if (response.data && response.data.email) {
                $('#email').val(response.data.email);
              }

            } else {

              Swal.fire({
                icon: 'error',
                title: 'Oops...',
                text: 'Form submission failed: ' + response.errors.join(', '),
              });

              console.log('Form submission failed:', response.errors);
            }
          },
          error: function(xhr, status, error) {

            Swal.fire({
              icon: 'error',
              title: 'AJAX Request Failed',
              text: 'Error: ' + error,
            });

            console.log('AJAX request failed:', error);
          }
        });
      }
      </script>

{% endblock %}