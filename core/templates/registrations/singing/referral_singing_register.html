{% extends 'include/base.html' %}

{% block title %}
    <title>WonderMom Fest 2025: Singing Competition ! Register Today</title>
    <meta name="og:title" content="WonderMom Fest 2025: Singing Competition ! Register Today">
{% endblock %}

{% load static %}
{% block content %}


    <section class="section sections-one" style="background-image: url('{% static "assets/images/bg/singing.webp" %}'); background-position: center center; background-repeat: no-repeat; background-size: cover;">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 align-self-center">
                    <div class="left-text-content">
  <div class="section-heading">
                            <h6>WonderMom Fest 2025</h6>
                            <h2>WonderMom Fest : Bump to Baby & Back to School Edition</h2>
                        </div>
<p style="margin-bottom: 12px;">For any doubts or support, please click here to WhatsApp us or contact us at <a href="https://wa.me/+************" target="_blank">+971 58 631 6069</a> .
                            <br><br>
                            Get Your Free Tickets Online: <a href="https://fest.thewondermom.club/tickets/{{ referrals.slug }}" target="_blank">Claim Your Tickets</a><br><br>

<b>Our Top Competitions & Registrations</b><br>
Mothers Registrations: <a href="https://fest.thewondermom.club/mother-register/{{ referrals.slug }}" target="_blank">Register Here</a><br>
New Mom Registrations: <a href="https://fest.thewondermom.club/mother-register/{{ referrals.slug }}" target="_blank">Register Here</a><br>
Fancy Dress Competitions: <a href="https://fest.thewondermom.club/fancy-register/{{ referrals.slug }}" target="_blank">Register Here</a><br>
Drawing Competitions: <a href="https://fest.thewondermom.club/drawing-register/{{ referrals.slug }}" target="_blank">Register Here</a><br>
Dancing Competitions: <a href="https://fest.thewondermom.club/dance-register/{{ referrals.slug }}" target="_blank">Register Here</a><br>
Baby Photo Competitions: <a href="https://fest.thewondermom.club/baby-photo-register/{{ referrals.slug }}" target="_blank">Register Here</a><br><br>

<b>Event Details</b><br>

Dates: September 7th & 8th, 2024<br>
Venue: Dubai</p>





                                           <div class="row">
    <div class="col-lg-12">
         <p style="height: 240px; color:black; overflow: auto; background-color:white; padding:10px;">
             <b><u>Guidelines for Singing Competition</u></b><br>
             <br>
             <b><u>Eligibility</u></b><br>Ages: 5-12<br>Language: English/Hindi/Arabic<br>
             <br>
             <b><u>Audition Process</u></b><br><br>

             <b><u>1) Initial Screening</u></b><br>
             Submit a performance video.<br>
             Send a 1-2 minute singing video via WhatsApp to <a href="https://wa.me/+************" target="_blank">+971 58 631 6069</a>.
             <br><br>

<b><u>2) Finalist Selection</u></b><br>
All the shared video will be sent to Jury and Top 5 winners will be announced at the Day of event. No Live Performance is there, the Top 5 Videos will be played on the main screen. Participants have a choice to share a new video.<br><br>

<b><u>Judging Criteria</u></b><br><br>

<b><u>Vocal Ability</u></b><br>
Pitch<br>
Tone<br>
Volume<br><br>

<b><u>Musicality</u></b><br>
Rhythm<br>
Expression<br><br>

<b><u>Performance</u></b><br>
Presentation<br><br>

<b><u>Winners</u></b><br>
Top 5 participants will be awarded.<br><br>

<b><u>Prizes</u></b><br>
Awards:  Winners’ Hamper, Certificate, and Magazine Feature.<br>
Certificates: All participants will receive a certificate of participation.<br>
         </p>
     </div>
 </div>
</div>
</div>

              <div class="col-lg-6">
                    <div class="contact-form">
                        <form id="contact"  method="post" enctype="multipart/form-data" onsubmit="submitForm(event)">
                            {% csrf_token %}
                          <div class="row">
                            <div class="col-lg-12">
                              <center><h6 style="color:blue; margin-bottom:10px;">Referred By {{ referrals.name }}</h6></center>
                                <h4>SINGING COMPETITION</h4>
                        <p style="color:red; margin-bottom: 5px; text-align: center;">Registration for this competition is now closed. However, feel free to join our other competitions and attend our event�tickets are free!</p>                            </div>

                            </div>
                            <div class="col-lg-12 col-sm-12">
                              <fieldset>
                                <input name="parents_name" type="text" id="parents_name" placeholder="Parent's Name *" required="">
                              </fieldset>
                            </div>

                               <div class="col-lg-12 col-sm-12">
                              <fieldset>
                                <input name="phone" type="text" id="phone" placeholder="Contact Number *" required="">
                              </fieldset>
                            </div>

                            <div class="col-lg-12 col-sm-12">
                              <fieldset>
                              <input name="email" type="email" id="email" pattern="[^ @]*@[^ @]*" placeholder="Email Address *" required="">
                            </fieldset>
                            </div>


                            <div class="col-lg-6 col-sm-12">
                              <fieldset>
                                <input name="child_name" type="text" id="child_name" placeholder="Child's Name *" required="">
                              </fieldset>
                            </div>

                              <div class="col-lg-6 col-sm-12">
                              <fieldset>
                                <input name="child_age" type="text" id="child_age" placeholder="Child's Age *" required="">
                              </fieldset>
                            </div>


                            <div class="col-lg-12 col-sm-12">
                                <fieldset>
                                  <input name="nationality" type="text" id="nationality" placeholder="Nationality *" required="">
                                </fieldset>
                              </div>

                              <div class="col-lg-12 col-sm-12">
                              <fieldset>
                            <textarea name="about" rows="6" id="message" placeholder="About Yourself (Child's Description for Social Media Post)" required=""></textarea>                                                  </fieldset>
                            </div>

                              <div class="col-lg-12 col-sm-12">
                                <fieldset>
                                  <input name="insta_link" type="text" id="insta_link" placeholder="Instagram Link">
                                </fieldset>
                              </div>

                               <div class="col-lg-12 col-sm-12">
                                <fieldset>
                                    <label>Child's Picture </label>
                                  <input name="photo" type="file" id="photo" placeholder="Child's Picture" style="padding-top:8px;">
                                </fieldset>
                              </div>

                            <div class="col-lg-12" style="margin-top: 20px;">
                              <fieldset>
                                <button type="submit" id="form-submit" class="main-button-icon">Submit Now</button>
                              </fieldset>
                            </div>


  <center> <div id="success-message" class="success-message" style="display: none;">
      <div class="col-lg-12">
  Singing Competition submitted successfully!
  </div></div></center>

<div id="error-message" class="error-message" style="display: none;">
    <div class="col-lg-12">
  Singing Competition submission failed. Please check your inputs and try again.
    </div></div>

                          </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>


<script>
       function submitForm(event) {
    event.preventDefault();

    // Create a FormData object from the form element
    var formData = new FormData($('#contact')[0]);

    $.ajax({
         url: '{% url 'referral_singing_register' slug=referrals.slug %}',
        type: 'post',
        data: formData,
        processData: false,  // Prevent jQuery from automatically processing the data
        contentType: false,  // Prevent jQuery from automatically setting the content type
        success: function(response) {
            if (response.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: 'Registration Successful !',
                });

                $('#contact')[0].reset();
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: 'Form submission failed: ' + Object.values(response.errors).join(', '),
                });

                console.log('Form submission failed:', response.errors);
            }
        },
        error: function(xhr, status, error) {
            Swal.fire({
                icon: 'error',
                title: 'AJAX Request Failed',
                text: 'Error: ' + error,
            });

            console.log('AJAX request failed:', error);
        }
    });
}

      </script>
{% endblock %}