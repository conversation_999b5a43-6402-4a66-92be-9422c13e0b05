{% extends 'include/base.html' %}

{% block title %}
    <title>WonderMom Fest 2025: Fancy Dress Competition ! Register Today</title>
    <meta name="og:title" content="WonderMom Fest 2025: Fancy Dress Competition ! Register Today">
{% endblock %}

{% load static %}
{% block content %}


<section class="section sections-one"
    style="background-image: url('{% static "assets/images/bg/fancy.webp" %}');
           background-position: center center;
           background-repeat: no-repeat;
           background-size: cover;">
<div class="container">
            <div class="row">
                <div class="col-lg-6 align-self-center">
                    <div class="left-text-content">
                        <div class="section-heading">
                            <h6>WonderMom Fest 2025</h6>
                            <h2>WonderMom Fest : Bump to Baby & Back to School Edition</h2>
                        </div>
                           <p style="margin-bottom: 12px;">For any doubts or support, please click here to WhatsApp us or contact us at <a href="https://wa.me/+971569064307" target="_blank">+971 56 906 4307</a> .
                            <br><br>
                            Get Your Free Tickets Online: <a href="https://fest.thewondermom.club/tickets/{{ referrals.slug }}" target="_blank">Claim Your Tickets</a><br><br>

<b>Our Top Competitions & Registrations</b><br>
Mothers Registrations: <a href="https://fest.thewondermom.club/mother-register/{{ referrals.slug }}" target="_blank">Register Here</a><br>
New Mom Registrations: <a href="https://fest.thewondermom.club/mother-register/{{ referrals.slug }}" target="_blank">Register Here</a><br>
Drawing Competitions: <a href="https://fest.thewondermom.club/drawing-register/{{ referrals.slug }}" target="_blank">Register Here</a><br>
Singing Competitions: <a href="https://fest.thewondermom.club/singing-register/{{ referrals.slug }}" target="_blank">Register Here</a><br>
Dancing Competitions: <a href="https://fest.thewondermom.club/dance-register/{{ referrals.slug }}" target="_blank">Register Here</a><br>
Baby Photo Competitions: <a href="https://fest.thewondermom.club/baby-photo-register/{{ referrals.slug }}" target="_blank">Register Here</a><br><br>

<b>Event Details</b><br>

Dates: September 7th & 8th, 2024<br>
Venue: Dubai, opposite Burj Khalifa</p>


                                               <div class="row">
                           <div class="col-lg-12">
                                <p style="height: 240px; color:black; overflow: auto; background-color:white; padding:10px;">
                                    <b><u>Guidelines for Fancy Dress Competition</u></b><br>
                                    <br>
                                    <b><u>Eligibility</u></b><br>Ages: 1-7<br><br>
                                    <b><u>Process</u></b><br>
All participants must arrive at the venue and explore the exhibition. The Jury will be observing all the participants and might also come and ask questions personally. Apart from Jury there will be silent observers as well in the event. After the internal discussion, Top 5 will be announced on the stage.<br><br>
<b><u>Judging Criteria</u></b><br><br>
<b><u>Creativity</u></b><br>
Originality<br>
Uniqueness<br><br>
                                    <b><u>Costume</u></b><br>
Design<br>
Detail<br><br>
<b><u>Presentation</u></b><br>
Stage presence<br>
Confidence<br><br>
<b><u>Winners</u></b><br>
Top 3 participants will be awarded and 2 consolations.<br><br>
<b><u>Prizes</u></b><br>
Awards: Winners’ Hamper, Certificate, and Magazine Feature<br>
Certificates: All participants will receive a certificate of participation<br>
                                </p>
                            </div>

                        </div>
                    </div>
                </div>
                  <div class="col-lg-6">
                    <div class="contact-form">
                        <form id="contact"  method="post" enctype="multipart/form-data" onsubmit="submitForm(event)">
                            {% csrf_token %}
                          <div class="row">
                            <div class="col-lg-12">
                              <center><h6 style="color:blue; margin-bottom:10px;">Referred By {{ referrals.name }}</h6></center>

                                <h4>FANCY DRESS COMPETITION</h4>
                            </div>
                            <div class="col-lg-12 col-sm-12">
                              <fieldset>
                                <input name="parents_name" type="text" id="parents_name" placeholder="Parent's Name *" required="">
                              </fieldset>
                            </div>

                               <div class="col-lg-12 col-sm-12">
                              <fieldset>
                                <input name="phone" type="text" id="phone" placeholder="Contact Number *" required="">
                              </fieldset>
                            </div>

                            <div class="col-lg-12 col-sm-12">
                              <fieldset>
                              <input name="email" type="email" id="email" pattern="[^ @]*@[^ @]*" placeholder="Email Address *" required="">
                            </fieldset>
                            </div>


                            <div class="col-lg-6 col-sm-12">
                              <fieldset>
                                <input name="child_name" type="text" id="child_name" placeholder="Child's Name *" required="">
                              </fieldset>
                            </div>

                              <div class="col-lg-6 col-sm-12">
                              <fieldset>
                                <input name="child_age" type="text" id="child_age" placeholder="Child's Age *" required="">
                              </fieldset>
                            </div>


                            <div class="col-lg-12 col-sm-12">
                                <fieldset>
                                  <input name="nationality" type="text" id="nationality" placeholder="Nationality *" required="">
                                </fieldset>
                              </div>

                              <div class="col-lg-12 col-sm-12">
                              <fieldset>
                            <textarea name="about" rows="6" id="message" placeholder="About Yourself (Child's Description for Social Media Post)" required=""></textarea>                                                  </fieldset>
                            </div>

                              <div class="col-lg-12 col-sm-12">
                                <fieldset>
                                  <input name="insta_link" type="text" id="insta_link" placeholder="Instagram Link">
                                </fieldset>
                              </div>

                               <div class="col-lg-12 col-sm-12">
                                <fieldset>
                                    <label>Child's Picture </label>
                                  <input name="photo" type="file" id="photo" placeholder="Child's Picture " style="padding-top:8px;">
                                </fieldset>
                              </div>

                            <div class="col-lg-12" style="margin-top: 20px;">
                              <fieldset>
                                <button type="submit" id="form-submit" class="main-button-icon">Submit Now</button>
                              </fieldset>
                            </div>


  <center> <div id="success-message" class="success-message" style="display: none;">
      <div class="col-lg-12">
  Fancy Competition submitted successfully!
  </div></div></center>

<div id="error-message" class="error-message" style="display: none;">
    <div class="col-lg-12">
  Fancy Competition submission failed. Please check your inputs and try again.
    </div></div>

                          </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>


<script>
       function submitForm(event) {
    event.preventDefault();

    // Create a FormData object from the form element
    var formData = new FormData($('#contact')[0]);

    $.ajax({
         url: '{% url 'referral_fancy_register' slug=referrals.slug %}',
        type: 'post',
        data: formData,
        processData: false,  // Prevent jQuery from automatically processing the data
        contentType: false,  // Prevent jQuery from automatically setting the content type
        success: function(response) {
            if (response.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: 'Registration Successful !',
                });

                $('#contact')[0].reset();
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: 'Form submission failed: ' + Object.values(response.errors).join(', '),
                });

                console.log('Form submission failed:', response.errors);
            }
        },
        error: function(xhr, status, error) {
            Swal.fire({
                icon: 'error',
                title: 'AJAX Request Failed',
                text: 'Error: ' + error,
            });

            console.log('AJAX request failed:', error);
        }
    });
}

      </script>

{% endblock %}