{% extends 'include/base.html' %}

{% block title %}
    <title>WonderMom Fest 2025: Mother Registration ! Register Today</title>
    <meta name="og:title" content="WonderMom Fest 2025: Mother Registration ! Register Today">
{% endblock %}

{% load static %}
{% block content %}



    <section class="section sections-one" style="background-image: url('{% static "assets/images/bg/mother_register.webp" %}'); background-position: center center; background-repeat: no-repeat; background-size: cover;">
        <div class="container">
            <div class="row">
                <div class="col-lg-6 align-self-center">
                    <div class="left-text-content">
                        <div class="section-heading">
                            <h6>WonderMom Fest 2025</h6>
                            <h2>WonderMom Fest : Bump to Baby & Back to School Edition</h2>
                        </div>
                          <p style="margin-bottom: 12px;">For any doubts or support, please click here to WhatsApp us or contact us at <a href="https://wa.me/+971545083789" target="_blank">+971 58 631 6069</a> .
                            <br><br>
                            Get Your Free Tickets Online: <a href="https://fest.thewondermom.club/tickets/{{ referrals.slug }}" target="_blank">Claim Your Tickets</a><br><br>

<b>Our Top Competitions</b><br>

Drawing Competitions: <a href="https://fest.thewondermom.club/drawing-register/{{ referrals.slug }}" target="_blank">Register Here</a><br>
Singing Competitions: <a href="https://fest.thewondermom.club/singing-register/{{ referrals.slug }}" target="_blank">Register Here</a><br>
Dancing Competitions: <a href="https://fest.thewondermom.club/dance-register/{{ referrals.slug }}" target="_blank">Register Here</a><br>
Fancy Dress Competitions: <a href="https://fest.thewondermom.club/fancy-register/{{ referrals.slug }}" target="_blank">Register Here</a><br>
Baby Photo Competitions: <a href="https://fest.thewondermom.club/baby-photo-register/{{ referrals.slug }}" target="_blank">Register Here</a><br><br>

<b>Event Details</b><br>

Dates: September 7th & 8th, 2024<br>
Venue: Dubai</p>
                        <br><br>

                        <div class="row">
                            <div class="col-lg-6">
                                <div class="phone">
                                    <i class="fa fa-phone"></i>
                                    <h4>Phone Numbers</h4>
                                    <span>
                                    <a href="tel:+971545083789">+971 58 631 6069</a><br>
                                    <a href="tel:+971505382042">+971 58 631 6069</a>
                                    </span>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="message">
                                    <i class="fa fa-envelope"></i>
                                    <h4>Emails</h4>
                                    <span><a href="mailto:<EMAIL>"><EMAIL></a>
                                     <a href="mailto:<EMAIL>"><EMAIL></a></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="contact-form">
<form id="contact" method="post" onsubmit="submitForm(event)" enctype="multipart/form-data">
                            {% csrf_token %}
                          <div class="row">
                            <div class="col-lg-12">
                                <center><h6 style="color:blue; margin-bottom:10px;">Referred By {{ referrals.name }}</h6></center>
                                <h4>Mother Registration</h4>
                            </div>
                               <div class="col-lg-12 col-sm-12">
                                <fieldset>
                                    <label>Mother's Picture </label>
                                  <input name="photo" type="file" id="photo" placeholder="Mother's Picture *" style="padding-top:8px;">
                                </fieldset>
                              </div>
                            <div class="col-lg-12 col-sm-12">
                              <fieldset>
                                <input name="name" type="text" id="name" placeholder="Name*" required="" >
                              </fieldset>
                            </div>

                            <div class="col-lg-12 col-sm-12">
                              <fieldset>
                              <input name="email" type="email" id="email" pattern="[^ @]*@[^ @]*" placeholder="Email Address *" required=""  >
                            </fieldset>
                            </div>


                            <div class="col-lg-12 col-sm-12">
                              <fieldset>
                                <input name="phone" type="text" id="phone" placeholder="Phone Number *" required=""  >
                              </fieldset>
                            </div>

                            <div class="col-lg-12 col-sm-12">
                                <fieldset>
                                  <input name="nationality" type="text" id="nationality" placeholder="Nationality *" required=""  >
                                </fieldset>
                              </div>



                             <div class="col-md-12 col-sm-12">
                              <fieldset>
                                <select name="age_group" id="age_group" required>
            <option value="" disabled selected>Choose Kids Age Limit *</option>
                                    <option name="Expecting Mom" id="Expecting Mom">Expecting Mom</option>
                                    <option name="0-3" id="0-3">0-3</option>
                                    <option name="3-12" id="3-12">3-12</option>
                                   <option name="Others" id="Others">Others</option>
                                </select>
                              </fieldset>
                            </div>


                             <div class="col-lg-12 col-sm-12">
                                <fieldset>
                                  <input name="emirates" type="text" id="emirates" placeholder="Emirates you stay in *" required=""  >
                                </fieldset>
                              </div>

                              <div class="col-lg-12 col-sm-12">
                                <fieldset>
                                  <input name="insta_link" type="text" id="insta_link" placeholder="Instagram Link">
                                </fieldset>
                              </div>

                            <div class="col-lg-12" style="margin-top: 20px;">
                              <fieldset>
  <button type="submit" id="form-submit" class="main-button-icon">Join Now</button>
                              </fieldset>
                            </div>

  <center> <div id="success-message" class="success-message" style="display: none;">
      <div class="col-lg-12">
Thank you for registering! Your tickets for you & your family have been confirmed!
  </div></div></center>

<div id="error-message" class="error-message" style="display: none;">
    <div class="col-lg-12">
  Mother Registration submission failed. Please check your inputs and try again.
    </div></div>
                          </div>
                        </form>

                    </div>
                </div>
            </div>
        </div>
    </section>

<script>

       function submitForm(event) {
    event.preventDefault();

    // Create a FormData object from the form element
    var formData = new FormData($('#contact')[0]);

    $.ajax({
         url: '{% url 'referral_mother_register' slug=referrals.slug %}',
        type: 'post',
        data: formData,
        processData: false,  // Prevent jQuery from automatically processing the data
        contentType: false,  // Prevent jQuery from automatically setting the content type
        success: function(response) {
            if (response.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Success!',
                    text: 'Thank you for registering! Your tickets for you & your family have been confirmed!',
                });

                $('#contact')[0].reset();
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Oops...',
                    text: 'Form submission failed: ' + Object.values(response.errors).join(', '),
                });

                console.log('Form submission failed:', response.errors);
            }
        },
        error: function(xhr, status, error) {
            Swal.fire({
                icon: 'error',
                title: 'AJAX Request Failed',
                text: 'Error: ' + error,
            });

            console.log('AJAX request failed:', error);
        }
    });
}

      </script>


{% endblock %}