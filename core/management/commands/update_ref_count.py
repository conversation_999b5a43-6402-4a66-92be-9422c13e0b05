from django.core.management.base import BaseCommand
from core.models import <PERSON><PERSON><PERSON>, Mother<PERSON>egister, <PERSON>cyRegister, DrawingRegister, SingingRegister, DanceRegister, \
    Tickets, BabyPhotoRegister


class Command(BaseCommand):
    help = 'Update ref_count for all referrals based on current registrations.'

    def handle(self, *args, **kwargs):
        for referral in Referral.objects.all():
            ref_count = (
                    MotherRegister.objects.filter(referrals=referral).count() +
                    FancyRegister.objects.filter(referrals=referral).count() +
                    DrawingRegister.objects.filter(referrals=referral).count() +
                    SingingRegister.objects.filter(referrals=referral).count() +
                    DanceRegister.objects.filter(referrals=referral).count() +
                    Tickets.objects.filter(referrals=referral).count() +
                    BabyPhotoRegister.objects.filter(referrals=referral).count()
            )
            referral.ref_count = ref_count
            referral.save()

        self.stdout.write(self.style.SUCCESS('Successfully updated ref_count for all referrals.'))
