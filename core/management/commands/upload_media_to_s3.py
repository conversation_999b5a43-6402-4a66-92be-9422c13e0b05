from django.core.files.storage import default_storage
from django.core.management.base import BaseCommand
from pathlib import Path

class Command(BaseCommand):
    help = 'Upload existing media files to S3'

    def handle(self, *args, **kwargs):
        media_root = Path('media/')  # Adjust the path if your media files are stored elsewhere
        for media_file in media_root.glob('**/*'):
            if media_file.is_file():
                with open(media_file, 'rb') as f:
                    file_name = str(media_file.relative_to(media_root))
                    default_storage.save(file_name, f)
                    self.stdout.write(f'Successfully uploaded {file_name}')
