from django.core.management.base import BaseCommand
from core.models import Referral


class Command(BaseCommand):
    help = 'Fix incorrect ref_count values in the Referral table.'

    def handle(self, *args, **kwargs):
        for referral in Referral.objects.all():
            try:
                if not isinstance(referral.ref_count, int):
                    referral.ref_count = 0
                    referral.save()
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'Error updating ref_count for Referral ID {referral.id}: {e}'))

        self.stdout.write(self.style.SUCCESS('Successfully fixed ref_count values for all referrals.'))
