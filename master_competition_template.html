{% extends 'include/base.html' %}

{% block title %}
    <title>WonderMom Fest 2025: {{competition_title}} ! Register Today</title>
    <meta name="og:title" content="WonderMom Fest 2025: {{competition_title}} ! Register Today">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
{% endblock %}

{% load static %}
{% block content %}

<section class="section sections-one"
    style="background-image: url('{% static "assets/images/bg/babyphoto.webp" %}');
           background-position: center center;
           background-repeat: no-repeat;
           background-size: cover;">
    <div class="container">
            <div class="row">
                <div class="col-lg-6 align-self-center">
                    <div class="left-text-content">
                        <div class="section-heading">
                            <h6>WonderMom Fest 2025</h6>
                            <h2>{{competition_name}}</h2>
                        </div>
                      <p style="margin-bottom: 12px;">For any doubts or support, please click here to WhatsApp us or contact us at <a href="https://wa.me/+971 52 937 6415" target="_blank">+971 52 937 6415</a> .
                            <br><br>
                            Get Your Free Tickets Online: <a href="https://fest.thewondermom.club/tickets" target="_blank">Claim Your Tickets</a><br><br>

<b>Other Competition Registrations</b><br>
Baby Photography: <a href="https://fest.thewondermom.club/baby-photography-register/" target="_blank">Register Here</a><br>
Canvas of Dream: <a href="https://fest.thewondermom.club/canvas-of-dream-register/" target="_blank">Register Here</a><br>
Character Carnival (Fancy Dress): <a href="https://fest.thewondermom.club/fancy-dress-register/" target="_blank">Register Here</a><br>
Card Making (MomCard Magic): <a href="https://fest.thewondermom.club/card-making-register/" target="_blank">Register Here</a><br>
Clay Creations: <a href="https://fest.thewondermom.club/clay-creations-register/" target="_blank">Register Here</a><br>
Colors in Motion: <a href="https://fest.thewondermom.club/colors-in-motion-register/" target="_blank">Register Here</a><br>
Eco Warrior: <a href="https://fest.thewondermom.club/eco-warrior-register/" target="_blank">Register Here</a><br>
Twinning with Mom: <a href="https://fest.thewondermom.club/twinning-with-mom-register/" target="_blank">Register Here</a><br>
Waste to Wow: <a href="https://fest.thewondermom.club/waste-to-wow-register/" target="_blank">Register Here</a><br><br>

<b>Event Details</b><br>
<strong>{{competition_name}}</strong><br>
{{competition_description}}<br><br>

Date: {{competition_date}}<br>
Venue: Dubai</p>

                        <div class="row">
    <div class="col-lg-12">
         <p style="height: 240px; color:black; overflow: auto; background-color:white; padding:10px;">
             {{competition_details|safe}}
         </p>
     </div>

 </div>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="right-text-content">
                        <div class="row">
                            <div class="col-lg-12">
                                <div class="section-heading">
                                    <h6>Register Now</h6>
                                    <h2>{{competition_name}} Registration</h2>
                                </div>
                            </div>
                            <div class="col-lg-12">
                                <form id="contact" action="" method="post" enctype="multipart/form-data">
                                    {% csrf_token %}
                                    <div class="row">
                                        <div class="col-lg-12">
                                            <fieldset>
                                                {{form.parents_name}}
                                            </fieldset>
                                        </div>
                                        <div class="col-lg-12">
                                            <fieldset>
                                                {{form.phone}}
                                            </fieldset>
                                        </div>
                                        <div class="col-lg-12">
                                            <fieldset>
                                                {{form.email}}
                                            </fieldset>
                                        </div>
                                        <div class="col-lg-12">
                                            <fieldset>
                                                {{form.child_name}}
                                            </fieldset>
                                        </div>
                                        <div class="col-lg-12">
                                            <fieldset>
                                                {{form.child_age}}
                                            </fieldset>
                                        </div>
                                        <div class="col-lg-12">
                                            <fieldset>
                                                {{form.nationality}}
                                            </fieldset>
                                        </div>
                                        <div class="col-lg-12">
                                            <fieldset>
                                                {{form.about}}
                                            </fieldset>
                                        </div>
                                        <div class="col-lg-12">
                                            <fieldset>
                                                {{form.photo}}
                                            </fieldset>
                                        </div>
                                        <div class="col-lg-12">
                                            <fieldset>
                                                {{form.insta_link}}
                                            </fieldset>
                                        </div>
                                        <div class="col-lg-12">
                                            <fieldset>
                                                <button type="submit" id="form-submit" class="main-button-icon">Register Now</button>
                                            </fieldset>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<script>
       function submitForm(event) {
    event.preventDefault();

    // Create a FormData object from the form element
    var formData = new FormData($('#contact')[0]);

    $.ajax({
         url: '{% url url_name %}',
        type: 'post',
        data: formData,
        processData: false,  // Prevent jQuery from automatically processing the data
        contentType: false,  // Prevent jQuery from automatically setting the content type
        success: function(response) {
            if (response.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Registration Successful!',
                    text: 'Thank you for registering. You will receive a confirmation email shortly.',
                    confirmButtonText: 'OK'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Optionally redirect or reset the form
                        $('#contact')[0].reset();
                    }
                });
            } else {
                // Handle form errors
                var errorMessage = 'Please correct the following errors:\n';
                for (var field in response.errors) {
                    errorMessage += '- ' + response.errors[field].join(', ') + '\n';
                }
                Swal.fire({
                    icon: 'error',
                    title: 'Registration Failed',
                    text: errorMessage,
                    confirmButtonText: 'OK'
                });
            }
        },
        error: function(xhr, status, error) {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'An error occurred while submitting the form. Please try again.',
                confirmButtonText: 'OK'
            });
        }
    });
}

// Attach the submitForm function to the form's submit event
$(document).ready(function() {
    $('#contact').on('submit', submitForm);
});
</script>

{% endblock %}
