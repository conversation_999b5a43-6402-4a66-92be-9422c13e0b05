# Django
*.log
*.pot
*.pyc
__pycache__/
local_settings.py
db.sqlite3
db.sqlite3-journal

# Environment variables
.env
.env.local
.env.production
.env.staging

# Virtual environment
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Media files (uploaded content)
media/
!media/.gitkeep

# Static files (collected)
static/
!static/.gitkeep

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo

# Django migrations (optional - uncomment if you want to ignore migrations)
# */migrations/*.py
# !*/migrations/__init__.py

# Backup files
*.bak
*.backup

# System files
.pid
*.pid

# Gunicorn
gunicorn.pid

# SSL certificates (if stored locally)
*.pem
*.key
*.crt
*.csr

# Configuration files with sensitive data
gunicorn_config.py
nginx_config.conf

# Node modules (if using frontend build tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
