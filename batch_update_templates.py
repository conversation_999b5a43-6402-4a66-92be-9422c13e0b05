#!/usr/bin/env python3

import os
import re

# Competition data from the document
competitions = {
    'baby-photography': {
        'title': 'Baby Photography Competition',
        'name': 'Baby Photography',
        'description': 'Calling all the photogenic smiles. Here\'s your chance to capture their cutest giggles, smiles, and candid moments!',
        'date': '20th September 2025',
        'url_name': 'baby_photography_register',
        'form_title': 'BABY PHOTOGRAPHY COMPETITION'
    },
    'clay-creations': {
        'title': 'Clay Creations Competition',
        'name': 'Clay Creations',
        'description': 'Let the magic of little hands come alive! Children with special needs are invited to mold their creativity into clay.',
        'date': '19th September 2025',
        'url_name': 'clay_creations_register',
        'form_title': 'CLAY CREATIONS COMPETITION'
    },
    'colors-in-motion': {
        'title': 'Colors in Motion Competition',
        'name': 'Colors in Motion',
        'description': 'A soothing space where specially abled / autistic children can express themselves through art — painting while listening to soft, calming music.',
        'date': '21st September 2025',
        'url_name': 'colors_in_motion_register',
        'form_title': 'COLORS IN MOTION COMPETITION'
    },
    'eco-warrior': {
        'title': 'Eco Warrior Competition',
        'name': 'Eco Warrior',
        'description': 'Calling all little eco-innovators! Bring your models, posters, or mini science projects to life as you pitch your green ideas for a cleaner, greener Earth.',
        'date': '20th September 2025',
        'url_name': 'eco_warrior_register',
        'form_title': 'ECO WARRIOR COMPETITION'
    },
    'fancy-dress': {
        'title': 'Character Carnival Competition',
        'name': 'Character Carnival: A Fancy Dress Competition',
        'description': 'Come one, come all, get your little one\'s pretty costumes on. Calling all the fashionistas for the much-anticipated fancy dress competition in town.',
        'date': '19th September 2025',
        'url_name': 'fancy_dress_register',
        'form_title': 'CHARACTER CARNIVAL COMPETITION'
    },
    'twinning-with-mom': {
        'title': 'Twinning with Mom Competition',
        'name': 'Twinning with Mom',
        'description': 'From matching outfits to matching acts — it\'s your chance to shine together! Moms and kids get to dress alike, pick a theme of their choice.',
        'date': '21st September 2025',
        'url_name': 'twinning_with_mom_register',
        'form_title': 'TWINNING WITH MOM COMPETITION'
    },
    'waste-to-wow': {
        'title': 'Waste to Wow Competition',
        'name': 'Waste to Wow',
        'description': 'Is your child an innovator creating waste to wonder? Let your child\'s creativity shine, as they turn trash to treasure.',
        'date': '21st September 2025',
        'url_name': 'waste_to_wow_register',
        'form_title': 'WASTE TO WOW COMPETITION'
    }
}

def update_template(comp_key, comp_data):
    template_path = f'core/templates/registrations/{comp_key}/{comp_key.replace("-", "_")}_register.html'
    
    if not os.path.exists(template_path):
        print(f"Template not found: {template_path}")
        return
    
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Update title
        content = re.sub(
            r'<title>WonderMom Fest \d+: [^<]+</title>',
            f'<title>WonderMom Fest 2025: {comp_data["title"]} ! Register Today</title>',
            content
        )
        
        # Update og:title
        content = re.sub(
            r'<meta name="og:title" content="[^"]+">',
            f'<meta name="og:title" content="WonderMom Fest 2025: {comp_data["title"]} ! Register Today">',
            content
        )
        
        # Update main heading
        content = re.sub(
            r'<h2>[^<]+</h2>',
            f'<h2>{comp_data["name"]}</h2>',
            content,
            count=1
        )
        
        # Update event details
        content = re.sub(
            r'Date: [^<]+<br>',
            f'Date: {comp_data["date"]}<br>',
            content
        )
        
        # Update JavaScript URL
        content = re.sub(
            r"url: '{% url '[^']+' %}',",
            f"url: '{{% url '{comp_data['url_name']}' %}},',",
            content
        )
        
        # Update form title
        content = re.sub(
            r'<h4>[^<]+</h4>',
            f'<h4>{comp_data["form_title"]}</h4>',
            content
        )
        
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ Updated: {comp_key}")
        
    except Exception as e:
        print(f"❌ Error updating {comp_key}: {e}")

# Update all templates
for comp_key, comp_data in competitions.items():
    update_template(comp_key, comp_data)

print("\n🎉 Batch update completed!")
